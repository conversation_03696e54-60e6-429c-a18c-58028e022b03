<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <title>智能体配置管理</title>
  <link rel="stylesheet" href="style.css" />
  <style>
    :root {
      --primary-color: #2563EB;
      --secondary-color: #60A5FA;
      --bg-color: #F8FAFC;
      --secondary-bg: #E2E8F0;
      --text-color: #1E293B;
      --text-secondary: #64748B;
      --success-color: #10B981;
      --danger-color: #EF4444;
      --warning-color: #F59E0B;
    }

    body {
      font-family: "Helvetica Neue", Arial, sans-serif;
      background-color: var(--bg-color);
      color: var(--text-color);
      margin: 0;
      padding: 0;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      padding-bottom: 15px;
      border-bottom: 1px solid var(--secondary-bg);
    }

    .header h1 {
      margin: 0;
      color: var(--primary-color);
    }

    .btn {
      padding: 10px 16px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .btn-primary {
      background-color: var(--primary-color);
      color: white;
    }

    .btn-primary:hover {
      background-color: #1D4ED8;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
    }

    .btn-success {
      background-color: var(--success-color);
      color: white;
    }

    .btn-success:hover {
      background-color: #059669;
    }

    .btn-danger {
      background-color: var(--danger-color);
      color: white;
    }

    .btn-danger:hover {
      background-color: #DC2626;
    }

    .btn-secondary {
      background-color: var(--secondary-bg);
      color: var(--text-color);
    }

    .btn-secondary:hover {
      background-color: #CBD5E1;
    }

    .agent-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .agent-card {
      background-color: white;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .agent-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    }

    .agent-header {
      padding: 15px;
      background: linear-gradient(135deg, var(--primary-color) 0%, #1E40AF 100%);
      color: white;
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .agent-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid white;
    }

    .agent-name {
      font-size: 18px;
      font-weight: 600;
      margin: 0;
    }

    .agent-type {
      font-size: 12px;
      background-color: rgba(255, 255, 255, 0.2);
      padding: 3px 8px;
      border-radius: 12px;
      margin-top: 5px;
      display: inline-block;
    }

    .agent-body {
      padding: 15px;
    }

    .agent-id {
      font-family: monospace;
      background-color: var(--secondary-bg);
      padding: 8px;
      border-radius: 4px;
      font-size: 12px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .agent-actions {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      margin-top: 15px;
    }

    /* Modal styles */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
    }

    .modal-overlay.active {
      opacity: 1;
      visibility: visible;
    }

    .modal {
      background-color: white;
      border-radius: 12px;
      width: 90%;
      max-width: 600px;
      max-height: 90vh;
      overflow-y: auto;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      transform: translateY(20px);
      transition: all 0.3s ease;
    }

    .modal-overlay.active .modal {
      transform: translateY(0);
    }

    .modal-header {
      padding: 20px;
      border-bottom: 1px solid var(--secondary-bg);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .modal-title {
      margin: 0;
      font-size: 20px;
      color: var(--primary-color);
    }

    .modal-close {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: var(--text-secondary);
    }

    .modal-body {
      padding: 20px;
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .form-control {
      width: 100%;
      padding: 10px;
      border: 1px solid var(--secondary-bg);
      border-radius: 6px;
      font-size: 16px;
      transition: border-color 0.3s ease;
    }

    .form-control:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }

    .form-select {
      width: 100%;
      padding: 10px;
      border: 1px solid var(--secondary-bg);
      border-radius: 6px;
      font-size: 16px;
      appearance: none;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%231E293B' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: right 10px center;
      background-size: 16px;
    }

    .avatar-grid {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 10px;
      margin-top: 10px;
    }

    .avatar-option {
      position: relative;
      cursor: pointer;
    }

    .avatar-option input[type="radio"] {
      position: absolute;
      opacity: 0;
      width: 0;
      height: 0;
    }

    .avatar-option img {
      width: 100%;
      height: 60px;
      object-fit: cover;
      border-radius: 6px;
      border: 2px solid transparent;
      transition: all 0.3s ease;
    }

    .avatar-option input[type="radio"]:checked + img {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.3);
      transform: scale(1.05);
    }

    .modal-footer {
      padding: 15px 20px;
      border-top: 1px solid var(--secondary-bg);
      display: flex;
      justify-content: flex-end;
      gap: 10px;
    }

    .alert {
      padding: 12px 16px;
      border-radius: 6px;
      margin-bottom: 20px;
    }

    .alert-success {
      background-color: #D1FAE5;
      color: #065F46;
      border-left: 4px solid var(--success-color);
    }

    .alert-danger {
      background-color: #FEE2E2;
      color: #B91C1C;
      border-left: 4px solid var(--danger-color);
    }

    .alert-warning {
      background-color: #FEF3C7;
      color: #92400E;
      border-left: 4px solid var(--warning-color);
    }

    .section-title {
      font-size: 18px;
      margin: 30px 0 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--secondary-bg);
      color: var(--primary-color);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .agent-list {
        grid-template-columns: 1fr;
      }
      
      .avatar-grid {
        grid-template-columns: repeat(4, 1fr);
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>智能体配置管理</h1>
      <button id="add-agent-btn" class="btn btn-primary">
        <span>➕ 添加智能体</span>
      </button>
    </div>

    <div id="alerts-container"></div>

    <h2 class="section-title">员工智能体</h2>
    <div id="employee-agents" class="agent-list">
      <!-- 员工智能体将在这里显示 -->
    </div>

    <h2 class="section-title">工具智能体</h2>
    <div id="tool-agents" class="agent-list">
      <!-- 工具智能体将在这里显示 -->
    </div>

    <!-- 添加/编辑智能体的模态框 -->
    <div id="agent-modal" class="modal-overlay">
      <div class="modal">
        <div class="modal-header">
          <h3 id="modal-title" class="modal-title">添加智能体</h3>
          <button class="modal-close" onclick="closeModal()">&times;</button>
        </div>
        <div class="modal-body">
          <form id="agent-form">
            <input type="hidden" id="agent-id-field" name="id">
            
            <div class="form-group">
              <label for="agent-name" class="form-label">智能体名称</label>
              <input type="text" id="agent-name" name="name" class="form-control" placeholder="例如：Coze助手" required>
            </div>
            
            <div class="form-group">
              <label for="agent-description" class="form-label">智能体描述</label>
              <input type="text" id="agent-description" name="description" class="form-control" placeholder="例如：通用AI助手，为您答疑解惑" required>
            </div>
            
            <div class="form-group">
              <label for="agent-type" class="form-label">智能体类型</label>
              <select id="agent-type" name="type" class="form-select" required>
                <option value="employee">员工</option>
                <option value="tool">工具</option>
              </select>
            </div>
            
            <div class="form-group">
              <label for="agent-bot-id" class="form-label">Bot ID</label>
              <input type="text" id="agent-bot-id" name="bot_id" class="form-control" placeholder="例如：7523880023146168366" required>
            </div>
            
            <div class="form-group">
              <label class="form-label">选择头像</label>
              <div id="avatar-grid" class="avatar-grid">
                <!-- 头像选项将在这里动态加载 -->
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" onclick="closeModal()">取消</button>
          <button id="save-agent-btn" class="btn btn-primary">保存</button>
        </div>
      </div>
    </div>

    <!-- 确认删除的模态框 -->
    <div id="confirm-modal" class="modal-overlay">
      <div class="modal">
        <div class="modal-header">
          <h3 class="modal-title">确认删除</h3>
          <button class="modal-close" onclick="closeConfirmModal()">&times;</button>
        </div>
        <div class="modal-body">
          <p>您确定要删除智能体 "<span id="delete-agent-name"></span>" 吗？此操作无法撤销。</p>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" onclick="closeConfirmModal()">取消</button>
          <button id="confirm-delete-btn" class="btn btn-danger">确认删除</button>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 存储智能体配置的本地存储键名
    const STORAGE_KEY = 'coze_agents_config';
    
    // 当前正在编辑的智能体ID
    let currentEditingId = null;
    let deleteAgentId = null;
    
    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 加载智能体配置
      loadAgents();
      
      // 加载头像选项
      loadAvatarOptions();
      
      // 添加事件监听器
      document.getElementById('add-agent-btn').addEventListener('click', openAddModal);
      document.getElementById('save-agent-btn').addEventListener('click', saveAgent);
      document.getElementById('confirm-delete-btn').addEventListener('click', confirmDeleteAgent);
    });
    
    // 加载智能体配置
    function loadAgents() {
      const agents = getAgentsFromStorage();
      
      // 清空现有的智能体列表
      document.getElementById('employee-agents').innerHTML = '';
      document.getElementById('tool-agents').innerHTML = '';
      
      // 如果没有智能体配置，显示默认的智能体
      if (agents.length === 0) {
        const defaultAgents = [
          {
            id: generateId(),
            name: 'Coze助手',
            description: '智能AI助手，为您答疑解惑',
            type: 'employee',
            bot_id: '7523880023146168366',
            avatar: 'touxiang/11.png'
          },
          {
            id: generateId(),
            name: '智能客服',
            description: '专业的客服助手',
            type: 'employee',
            bot_id: '7523880023146168366',
            avatar: 'touxiang/12.png'
          },
          {
            id: generateId(),
            name: '学习助手',
            description: '帮助您学习和成长',
            type: 'tool',
            bot_id: '7523880023146168366',
            avatar: 'touxiang/13.png'
          },
          {
            id: generateId(),
            name: '编程助手',
            description: '代码编写和技术问题',
            type: 'tool',
            bot_id: '7523880023146168366',
            avatar: 'touxiang/16.png'
          }
        ];
        
        // 保存默认智能体配置
        saveAgentsToStorage(defaultAgents);
        
        // 重新加载智能体
        loadAgents();
        return;
      }
      
      // 渲染智能体卡片
      agents.forEach(agent => {
        const agentCard = createAgentCard(agent);
        
        if (agent.type === 'employee') {
          document.getElementById('employee-agents').appendChild(agentCard);
        } else {
          document.getElementById('tool-agents').appendChild(agentCard);
        }
      });
    }
    
    // 创建智能体卡片
    function createAgentCard(agent) {
      const card = document.createElement('div');
      card.className = 'agent-card';
      card.innerHTML = `
        <div class="agent-header">
          <img src="${agent.avatar}" alt="${agent.name}" class="agent-avatar">
          <div>
            <h3 class="agent-name">${agent.name}</h3>
            <span class="agent-type">${agent.type === 'employee' ? '员工' : '工具'}</span>
          </div>
        </div>
        <div class="agent-body">
          <p>${agent.description}</p>
          <div class="agent-id">Bot ID: ${agent.bot_id}</div>
          <div class="agent-actions">
            <button class="btn btn-secondary edit-btn" data-id="${agent.id}">编辑</button>
            <button class="btn btn-danger delete-btn" data-id="${agent.id}" data-name="${agent.name}">删除</button>
          </div>
        </div>
      `;
      
      // 添加编辑按钮事件
      card.querySelector('.edit-btn').addEventListener('click', function() {
        openEditModal(agent.id);
      });
      
      // 添加删除按钮事件
      card.querySelector('.delete-btn').addEventListener('click', function() {
        openDeleteConfirmModal(agent.id, agent.name);
      });
      
      return card;
    }
    
    // 加载头像选项
    async function loadAvatarOptions() {
      try {
        // 这里我们假设已经知道touxiang文件夹中有哪些图片
        // 在实际应用中，您可能需要通过API获取文件列表
        const avatarFiles = [
          'touxiang/11.png', 'touxiang/12.png', 'touxiang/13.png', 'touxiang/16.png',
          'touxiang/19.png', 'touxiang/21.png', 'touxiang/213.png', 'touxiang/3.png',
          'touxiang/4.png', 'touxiang/5.png', 'touxiang/7.png', 'touxiang/8.png',
          'touxiang/9.png', 'touxiang/abca.png', 'touxiang/abgag.png', 'touxiang/afaf.png',
          'touxiang/aga.png', 'touxiang/agag.png', 'touxiang/agasf.png', 'touxiang/agasga.png'
        ];
        
        const avatarGrid = document.getElementById('avatar-grid');
        avatarGrid.innerHTML = '';
        
        avatarFiles.forEach((file, index) => {
          const label = document.createElement('label');
          label.className = 'avatar-option';
          
          const input = document.createElement('input');
          input.type = 'radio';
          input.name = 'avatar';
          input.value = file;
          input.required = true;
          if (index === 0) input.checked = true;
          
          const img = document.createElement('img');
          img.src = file;
          img.alt = `头像选项 ${index + 1}`;
          
          label.appendChild(input);
          label.appendChild(img);
          avatarGrid.appendChild(label);
        });
      } catch (error) {
        console.error('加载头像选项失败:', error);
        showAlert('加载头像选项失败，请刷新页面重试。', 'danger');
      }
    }
    
    // 打开添加智能体模态框
    function openAddModal() {
      currentEditingId = null;
      document.getElementById('modal-title').textContent = '添加智能体';
      document.getElementById('agent-form').reset();
      document.getElementById('agent-id-field').value = '';
      
      // 默认选中第一个头像
      const firstAvatar = document.querySelector('#avatar-grid input[type="radio"]');
      if (firstAvatar) firstAvatar.checked = true;
      
      openModal();
    }
    
    // 打开编辑智能体模态框
    function openEditModal(agentId) {
      const agents = getAgentsFromStorage();
      const agent = agents.find(a => a.id === agentId);
      
      if (!agent) {
        showAlert('找不到要编辑的智能体。', 'danger');
        return;
      }
      
      currentEditingId = agentId;
      document.getElementById('modal-title').textContent = '编辑智能体';
      
      // 填充表单
      document.getElementById('agent-id-field').value = agent.id;
      document.getElementById('agent-name').value = agent.name;
      document.getElementById('agent-description').value = agent.description;
      document.getElementById('agent-type').value = agent.type;
      document.getElementById('agent-bot-id').value = agent.bot_id;
      
      // 选中对应的头像
      const avatarInput = document.querySelector(`#avatar-grid input[value="${agent.avatar}"]`);
      if (avatarInput) avatarInput.checked = true;
      
      openModal();
    }
    
    // 打开删除确认模态框
    function openDeleteConfirmModal(agentId, agentName) {
      deleteAgentId = agentId;
      document.getElementById('delete-agent-name').textContent = agentName;
      document.getElementById('confirm-modal').classList.add('active');
    }
    
    // 关闭删除确认模态框
    function closeConfirmModal() {
      deleteAgentId = null;
      document.getElementById('confirm-modal').classList.remove('active');
    }
    
    // 确认删除智能体
    function confirmDeleteAgent() {
      if (!deleteAgentId) return;
      
      const agents = getAgentsFromStorage();
      const updatedAgents = agents.filter(agent => agent.id !== deleteAgentId);
      
      saveAgentsToStorage(updatedAgents);
      loadAgents();
      
      showAlert('智能体已成功删除。', 'success');
      closeConfirmModal();
    }
    
    // 保存智能体
    function saveAgent() {
      const form = document.getElementById('agent-form');
      
      // 表单验证
      if (!form.checkValidity()) {
        form.reportValidity();
        return;
      }
      
      const agentId = document.getElementById('agent-id-field').value || generateId();
      const name = document.getElementById('agent-name').value;
      const description = document.getElementById('agent-description').value;
      const type = document.getElementById('agent-type').value;
      const botId = document.getElementById('agent-bot-id').value;
      const avatar = document.querySelector('#avatar-grid input[type="radio"]:checked').value;
      
      const agents = getAgentsFromStorage();
      
      if (currentEditingId) {
        // 编辑现有智能体
        const index = agents.findIndex(agent => agent.id === currentEditingId);
        if (index !== -1) {
          agents[index] = {
            id: agentId,
            name,
            description,
            type,
            bot_id: botId,
            avatar
          };
        }
      } else {
        // 添加新智能体
        agents.push({
          id: agentId,
          name,
          description,
          type,
          bot_id: botId,
          avatar
        });
      }
      
      saveAgentsToStorage(agents);
      loadAgents();
      
      showAlert(`智能体 "${name}" 已成功${currentEditingId ? '更新' : '添加'}。`, 'success');
      closeModal();
    }
    
    // 从本地存储获取智能体配置
    function getAgentsFromStorage() {
      const agentsJson = localStorage.getItem(STORAGE_KEY);
      return agentsJson ? JSON.parse(agentsJson) : [];
    }
    
    // 保存智能体配置到本地存储
    function saveAgentsToStorage(agents) {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(agents));
    }
    
    // 生成唯一ID
    function generateId() {
      return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
    }
    
    // 打开模态框
    function openModal() {
      document.getElementById('agent-modal').classList.add('active');
    }
    
    // 关闭模态框
    function closeModal() {
      document.getElementById('agent-modal').classList.remove('active');
    }
    
    // 显示提示信息
    function showAlert(message, type) {
      const alertsContainer = document.getElementById('alerts-container');
      
      const alert = document.createElement('div');
      alert.className = `alert alert-${type}`;
      alert.textContent = message;
      
      alertsContainer.appendChild(alert);
      
      // 5秒后自动移除提示
      setTimeout(() => {
        alert.remove();
      }, 5000);
    }
  </script>
</body>
</html>