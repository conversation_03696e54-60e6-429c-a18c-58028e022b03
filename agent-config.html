<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <title>智能体配置管理</title>
  <link rel="stylesheet" href="style.css" />
  <style>
    :root {
      --primary-color: #2563EB;
      --secondary-color: #60A5FA;
      --bg-color: #F8FAFC;
      --secondary-bg: #E2E8F0;
      --text-color: #1E293B;
      --text-secondary: #64748B;
      --success-color: #10B981;
      --danger-color: #EF4444;
      --warning-color: #F59E0B;
    }

    body {
      font-family: "Helvetica Neue", Arial, sans-serif;
      background-color: var(--bg-color);
      color: var(--text-color);
      margin: 0;
      padding: 0;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      padding-bottom: 15px;
      border-bottom: 1px solid var(--secondary-bg);
    }

    .header h1 {
      margin: 0;
      color: var(--primary-color);
    }

    .btn {
      padding: 10px 16px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .btn-primary {
      background-color: var(--primary-color);
      color: white;
    }

    .btn-primary:hover {
      background-color: #1D4ED8;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
    }

    .btn-success {
      background-color: var(--success-color);
      color: white;
    }

    .btn-success:hover {
      background-color: #059669;
    }

    .btn-danger {
      background-color: var(--danger-color);
      color: white;
    }

    .btn-danger:hover {
      background-color: #DC2626;
    }

    .btn-secondary {
      background-color: var(--secondary-bg);
      color: var(--text-color);
    }

    .btn-secondary:hover {
      background-color: #CBD5E1;
    }

    .agent-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .agent-card {
      background-color: white;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .agent-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    }

    .agent-header {
      padding: 15px;
      background: linear-gradient(135deg, var(--primary-color) 0%, #1E40AF 100%);
      color: white;
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .agent-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid white;
    }

    .agent-name {
      font-size: 18px;
      font-weight: 600;
      margin: 0;
    }

    .agent-type {
      font-size: 12px;
      background-color: rgba(255, 255, 255, 0.2);
      padding: 3px 8px;
      border-radius: 12px;
      margin-top: 5px;
      display: inline-block;
    }

    .agent-body {
      padding: 15px;
    }

    .agent-id {
      font-family: monospace;
      background-color: var(--secondary-bg);
      padding: 8px;
      border-radius: 4px;
      font-size: 12px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .agent-actions {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      margin-top: 15px;
    }

    /* Modal styles */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
    }

    .modal-overlay.active {
      opacity: 1;
      visibility: visible;
    }

    .modal {
      background-color: white;
      border-radius: 12px;
      width: 90%;
      max-width: 600px;
      max-height: 90vh;
      overflow-y: auto;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      transform: translateY(20px);
      transition: all 0.3s ease;
    }

    .modal-overlay.active .modal {
      transform: translateY(0);
    }

    .modal-header {
      padding: 20px;
      border-bottom: 1px solid var(--secondary-bg);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .modal-title {
      margin: 0;
      font-size: 20px;
      color: var(--primary-color);
    }

    .modal-close {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: var(--text-secondary);
    }

    .modal-body {
      padding: 20px;
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .form-control {
      width: 100%;
      padding: 10px;
      border: 1px solid var(--secondary-bg);
      border-radius: 6px;
      font-size: 16px;
      transition: border-color 0.3s ease;
    }

    .form-control:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }

    .form-select {
      width: 100%;
      padding: 10px;
      border: 1px solid var(--secondary-bg);
      border-radius: 6px;
      font-size: 16px;
      appearance: none;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%231E293B' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: right 10px center;
      background-size: 16px;
    }

    .avatar-grid {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 10px;
      margin-top: 10px;
    }

    .avatar-option {
      position: relative;
      cursor: pointer;
    }

    .avatar-option input[type="radio"] {
      position: absolute;
      opacity: 0;
      width: 0;
      height: 0;
    }

    .avatar-option img {
      width: 100%;
      height: 60px;
      object-fit: cover;
      border-radius: 6px;
      border: 2px solid transparent;
      transition: all 0.3s ease;
    }

    .avatar-option input[type="radio"]:checked + img {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.3);
      transform: scale(1.05);
    }

    .modal-footer {
      padding: 15px 20px;
      border-top: 1px solid var(--secondary-bg);
      display: flex;
      justify-content: flex-end;
      gap: 10px;
    }

    .alert {
      padding: 12px 16px;
      border-radius: 6px;
      margin-bottom: 20px;
    }

    .alert-success {
      background-color: #D1FAE5;
      color: #065F46;
      border-left: 4px solid var(--success-color);
    }

    .alert-danger {
      background-color: #FEE2E2;
      color: #B91C1C;
      border-left: 4px solid var(--danger-color);
    }

    .alert-warning {
      background-color: #FEF3C7;
      color: #92400E;
      border-left: 4px solid var(--warning-color);
    }

    .section-title {
      font-size: 18px;
      margin: 30px 0 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--secondary-bg);
      color: var(--primary-color);
    }

    /* 分类管理样式 */
    .categories-container {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-bottom: 30px;
    }

    .category-item {
      background-color: white;
      border-radius: 8px;
      padding: 15px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      min-width: 200px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .category-name {
      font-weight: 500;
      color: var(--text-color);
    }

    .category-actions {
      display: flex;
      gap: 8px;
    }

    .category-actions .btn {
      padding: 5px 10px;
      font-size: 12px;
    }

    /* 开场白编辑区域样式 */
    .form-textarea {
      width: 100%;
      padding: 10px;
      border: 1px solid var(--secondary-bg);
      border-radius: 6px;
      font-size: 16px;
      font-family: inherit;
      resize: vertical;
      min-height: 80px;
      transition: border-color 0.3s ease;
    }

    .form-textarea:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }

    .text-warning {
      color: var(--warning-color);
      font-size: 14px;
      margin-top: 10px;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .agent-list {
        grid-template-columns: 1fr;
      }

      .avatar-grid {
        grid-template-columns: repeat(4, 1fr);
      }

      .categories-container {
        flex-direction: column;
      }

      .category-item {
        min-width: auto;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>智能体配置管理</h1>
      <div>
        <button id="add-category-btn" class="btn btn-secondary">
          <span>➕ 添加分类</span>
        </button>
        <button id="add-agent-btn" class="btn btn-primary">
          <span>➕ 添加智能体</span>
        </button>
      </div>
    </div>

    <div id="alerts-container"></div>

    <!-- 分类管理区域 -->
    <h2 class="section-title">分类管理</h2>
    <div id="categories-list" class="categories-container">
      <!-- 分类将在这里显示 -->
    </div>

    <!-- 智能体管理区域 -->
    <h2 class="section-title">智能体管理</h2>
    <div id="all-agents" class="agent-list">
      <!-- 所有智能体将在这里显示 -->
    </div>

    <!-- 添加/编辑智能体的模态框 -->
    <div id="agent-modal" class="modal-overlay">
      <div class="modal">
        <div class="modal-header">
          <h3 id="modal-title" class="modal-title">添加智能体</h3>
          <button class="modal-close" onclick="closeModal()">&times;</button>
        </div>
        <div class="modal-body">
          <form id="agent-form">
            <input type="hidden" id="agent-id-field" name="id">
            
            <div class="form-group">
              <label for="agent-name" class="form-label">智能体名称</label>
              <input type="text" id="agent-name" name="name" class="form-control" placeholder="例如：Coze助手" required>
            </div>
            
            <div class="form-group">
              <label for="agent-description" class="form-label">智能体描述</label>
              <input type="text" id="agent-description" name="description" class="form-control" placeholder="例如：通用AI助手，为您答疑解惑" required>
            </div>
            
            <div class="form-group">
              <label for="agent-category" class="form-label">所属分类</label>
              <select id="agent-category" name="category" class="form-select" required>
                <!-- 分类选项将在这里动态加载 -->
              </select>
            </div>
            
            <div class="form-group">
              <label for="agent-bot-id" class="form-label">Bot ID</label>
              <input type="text" id="agent-bot-id" name="bot_id" class="form-control" placeholder="例如：7523880023146168366" required>
            </div>

            <div class="form-group">
              <label for="agent-greeting" class="form-label">开场白</label>
              <textarea id="agent-greeting" name="greeting" class="form-textarea" placeholder="请输入智能体的开场白消息" rows="3">让我们一起学习进步！有什么学习问题都可以问我。</textarea>
            </div>

            <div class="form-group">
              <label class="form-label">选择头像</label>
              <div id="avatar-grid" class="avatar-grid">
                <!-- 头像选项将在这里动态加载 -->
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" onclick="closeModal()">取消</button>
          <button id="save-agent-btn" class="btn btn-primary">保存</button>
        </div>
      </div>
    </div>

    <!-- 分类编辑模态框 -->
    <div id="category-modal" class="modal-overlay">
      <div class="modal">
        <div class="modal-header">
          <h3 id="category-modal-title" class="modal-title">添加分类</h3>
          <button class="modal-close" onclick="closeCategoryModal()">&times;</button>
        </div>
        <div class="modal-body">
          <form id="category-form">
            <input type="hidden" id="category-id-field" name="id">

            <div class="form-group">
              <label for="category-name" class="form-label">分类名称</label>
              <input type="text" id="category-name" name="name" class="form-control" placeholder="例如：员工助手" required>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" onclick="closeCategoryModal()">取消</button>
          <button id="save-category-btn" class="btn btn-primary">保存</button>
        </div>
      </div>
    </div>

    <!-- 确认删除智能体的模态框 -->
    <div id="confirm-modal" class="modal-overlay">
      <div class="modal">
        <div class="modal-header">
          <h3 class="modal-title">确认删除</h3>
          <button class="modal-close" onclick="closeConfirmModal()">&times;</button>
        </div>
        <div class="modal-body">
          <p>您确定要删除智能体 "<span id="delete-agent-name"></span>" 吗？此操作无法撤销。</p>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" onclick="closeConfirmModal()">取消</button>
          <button id="confirm-delete-btn" class="btn btn-danger">确认删除</button>
        </div>
      </div>
    </div>

    <!-- 确认删除分类的模态框 -->
    <div id="confirm-category-modal" class="modal-overlay">
      <div class="modal">
        <div class="modal-header">
          <h3 class="modal-title">确认删除分类</h3>
          <button class="modal-close" onclick="closeConfirmCategoryModal()">&times;</button>
        </div>
        <div class="modal-body">
          <p>您确定要删除分类 "<span id="delete-category-name"></span>" 吗？</p>
          <p class="text-warning">注意：删除分类后，该分类下的所有智能体将被移动到"未分类"中。</p>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" onclick="closeConfirmCategoryModal()">取消</button>
          <button id="confirm-delete-category-btn" class="btn btn-danger">确认删除</button>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 存储智能体配置的本地存储键名
    const STORAGE_KEY = 'coze_agents_config';
    const CATEGORIES_STORAGE_KEY = 'coze_categories_config';

    // 当前正在编辑的智能体ID和分类ID
    let currentEditingId = null;
    let deleteAgentId = null;
    let currentEditingCategoryId = null;
    let deleteCategoryId = null;
    
    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 加载分类配置
      loadCategories();

      // 加载智能体配置
      loadAgents();

      // 加载头像选项
      loadAvatarOptions();

      // 添加事件监听器
      document.getElementById('add-category-btn').addEventListener('click', openAddCategoryModal);
      document.getElementById('add-agent-btn').addEventListener('click', openAddModal);
      document.getElementById('save-category-btn').addEventListener('click', saveCategory);
      document.getElementById('save-agent-btn').addEventListener('click', saveAgent);
      document.getElementById('confirm-delete-btn').addEventListener('click', confirmDeleteAgent);
      document.getElementById('confirm-delete-category-btn').addEventListener('click', confirmDeleteCategory);
    });
    
    // 加载分类配置
    function loadCategories() {
      const categories = getCategoriesFromStorage();
      const categoriesContainer = document.getElementById('categories-list');

      // 清空现有的分类列表
      categoriesContainer.innerHTML = '';

      // 如果没有分类配置，创建默认分类
      if (categories.length === 0) {
        const defaultCategories = [
          { id: generateId(), name: '员工助手' },
          { id: generateId(), name: '工具助手' },
          { id: generateId(), name: '未分类' }
        ];

        saveCategoriesStorage(defaultCategories);
        loadCategories();
        return;
      }

      // 渲染分类项
      categories.forEach(category => {
        const categoryItem = createCategoryItem(category);
        categoriesContainer.appendChild(categoryItem);
      });

      // 更新智能体表单中的分类选项
      updateCategoryOptions();
    }

    // 加载智能体配置
    function loadAgents() {
      const agents = getAgentsFromStorage();
      const categories = getCategoriesFromStorage();

      // 清空现有的智能体列表
      document.getElementById('all-agents').innerHTML = '';

      // 如果没有智能体配置，显示默认的智能体
      if (agents.length === 0) {
        // 确保有默认分类
        if (categories.length === 0) {
          loadCategories();
        }

        const updatedCategories = getCategoriesFromStorage();
        const defaultAgents = [
          {
            id: generateId(),
            name: 'Coze助手',
            description: '智能AI助手，为您答疑解惑',
            category: updatedCategories.find(c => c.name === '员工助手')?.id || updatedCategories[0]?.id,
            bot_id: '7523880023146168366',
            avatar: 'touxiang/11.png',
            greeting: '您好！我是您的专属AI助手，有什么可以帮助您的吗？'
          },
          {
            id: generateId(),
            name: '智能客服',
            description: '专业的客服助手',
            category: updatedCategories.find(c => c.name === '员工助手')?.id || updatedCategories[0]?.id,
            bot_id: '7523880023146168366',
            avatar: 'touxiang/12.png',
            greeting: '欢迎咨询！我是智能客服，随时为您提供专业服务。'
          },
          {
            id: generateId(),
            name: '学习助手',
            description: '帮助您学习和成长',
            category: updatedCategories.find(c => c.name === '工具助手')?.id || updatedCategories[1]?.id,
            bot_id: '7523880023146168366',
            avatar: 'touxiang/13.png',
            greeting: '让我们一起学习进步！有什么学习问题都可以问我。'
          },
          {
            id: generateId(),
            name: '编程助手',
            description: '代码编写和技术问题',
            category: updatedCategories.find(c => c.name === '工具助手')?.id || updatedCategories[1]?.id,
            bot_id: '7523880023146168366',
            avatar: 'touxiang/16.png',
            greeting: '我是您的编程伙伴，代码问题、技术难题都可以找我！'
          }
        ];

        // 保存默认智能体配置
        saveAgentsToStorage(defaultAgents);

        // 重新加载智能体
        loadAgents();
        return;
      }

      // 渲染所有智能体卡片
      agents.forEach(agent => {
        const agentCard = createAgentCard(agent);
        document.getElementById('all-agents').appendChild(agentCard);
      });
    }
    
    // 创建分类项
    function createCategoryItem(category) {
      const item = document.createElement('div');
      item.className = 'category-item';
      item.innerHTML = `
        <span class="category-name">${category.name}</span>
        <div class="category-actions">
          <button class="btn btn-secondary edit-category-btn" data-id="${category.id}">编辑</button>
          <button class="btn btn-danger delete-category-btn" data-id="${category.id}" data-name="${category.name}">删除</button>
        </div>
      `;

      // 添加编辑按钮事件
      item.querySelector('.edit-category-btn').addEventListener('click', function() {
        openEditCategoryModal(category.id);
      });

      // 添加删除按钮事件
      item.querySelector('.delete-category-btn').addEventListener('click', function() {
        openDeleteCategoryConfirmModal(category.id, category.name);
      });

      return item;
    }

    // 创建智能体卡片
    function createAgentCard(agent) {
      const categories = getCategoriesFromStorage();
      const category = categories.find(c => c.id === agent.category);
      const categoryName = category ? category.name : '未分类';

      const card = document.createElement('div');
      card.className = 'agent-card';
      card.innerHTML = `
        <div class="agent-header">
          <img src="${agent.avatar}" alt="${agent.name}" class="agent-avatar">
          <div>
            <h3 class="agent-name">${agent.name}</h3>
            <span class="agent-type">${categoryName}</span>
          </div>
        </div>
        <div class="agent-body">
          <p>${agent.description}</p>
          ${agent.greeting ? `<p><strong>开场白：</strong>${agent.greeting}</p>` : ''}
          <div class="agent-id">Bot ID: ${agent.bot_id}</div>
          <div class="agent-actions">
            <button class="btn btn-secondary edit-btn" data-id="${agent.id}">编辑</button>
            <button class="btn btn-danger delete-btn" data-id="${agent.id}" data-name="${agent.name}">删除</button>
          </div>
        </div>
      `;

      // 添加编辑按钮事件
      card.querySelector('.edit-btn').addEventListener('click', function() {
        openEditModal(agent.id);
      });

      // 添加删除按钮事件
      card.querySelector('.delete-btn').addEventListener('click', function() {
        openDeleteConfirmModal(agent.id, agent.name);
      });

      return card;
    }
    
    // 加载头像选项
    async function loadAvatarOptions() {
      try {
        // 这里我们假设已经知道touxiang文件夹中有哪些图片
        // 在实际应用中，您可能需要通过API获取文件列表
        const avatarFiles = [
          'touxiang/11.png', 'touxiang/12.png', 'touxiang/13.png', 'touxiang/16.png',
          'touxiang/19.png', 'touxiang/21.png', 'touxiang/213.png', 'touxiang/3.png',
          'touxiang/4.png', 'touxiang/5.png', 'touxiang/7.png', 'touxiang/8.png',
          'touxiang/9.png', 'touxiang/abca.png', 'touxiang/abgag.png', 'touxiang/afaf.png',
          'touxiang/aga.png', 'touxiang/agag.png', 'touxiang/agasf.png', 'touxiang/agasga.png'
        ];
        
        const avatarGrid = document.getElementById('avatar-grid');
        avatarGrid.innerHTML = '';
        
        avatarFiles.forEach((file, index) => {
          const label = document.createElement('label');
          label.className = 'avatar-option';
          
          const input = document.createElement('input');
          input.type = 'radio';
          input.name = 'avatar';
          input.value = file;
          input.required = true;
          if (index === 0) input.checked = true;
          
          const img = document.createElement('img');
          img.src = file;
          img.alt = `头像选项 ${index + 1}`;
          
          label.appendChild(input);
          label.appendChild(img);
          avatarGrid.appendChild(label);
        });
      } catch (error) {
        console.error('加载头像选项失败:', error);
        showAlert('加载头像选项失败，请刷新页面重试。', 'danger');
      }
    }
    
    // 更新分类选项
    function updateCategoryOptions() {
      const categories = getCategoriesFromStorage();
      const categorySelect = document.getElementById('agent-category');

      categorySelect.innerHTML = '';
      categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.id;
        option.textContent = category.name;
        categorySelect.appendChild(option);
      });
    }

    // 打开添加分类模态框
    function openAddCategoryModal() {
      currentEditingCategoryId = null;
      document.getElementById('category-modal-title').textContent = '添加分类';
      document.getElementById('category-form').reset();
      document.getElementById('category-id-field').value = '';
      openCategoryModal();
    }

    // 打开编辑分类模态框
    function openEditCategoryModal(categoryId) {
      const categories = getCategoriesFromStorage();
      const category = categories.find(c => c.id === categoryId);

      if (!category) {
        showAlert('找不到要编辑的分类。', 'danger');
        return;
      }

      currentEditingCategoryId = categoryId;
      document.getElementById('category-modal-title').textContent = '编辑分类';
      document.getElementById('category-id-field').value = category.id;
      document.getElementById('category-name').value = category.name;
      openCategoryModal();
    }

    // 打开添加智能体模态框
    function openAddModal() {
      currentEditingId = null;
      document.getElementById('modal-title').textContent = '添加智能体';
      document.getElementById('agent-form').reset();
      document.getElementById('agent-id-field').value = '';

      // 更新分类选项
      updateCategoryOptions();

      // 设置默认开场白
      document.getElementById('agent-greeting').value = '让我们一起学习进步！有什么学习问题都可以问我。';

      // 默认选中第一个头像
      const firstAvatar = document.querySelector('#avatar-grid input[type="radio"]');
      if (firstAvatar) firstAvatar.checked = true;

      openModal();
    }
    
    // 打开编辑智能体模态框
    function openEditModal(agentId) {
      const agents = getAgentsFromStorage();
      const agent = agents.find(a => a.id === agentId);

      if (!agent) {
        showAlert('找不到要编辑的智能体。', 'danger');
        return;
      }

      currentEditingId = agentId;
      document.getElementById('modal-title').textContent = '编辑智能体';

      // 更新分类选项
      updateCategoryOptions();

      // 填充表单
      document.getElementById('agent-id-field').value = agent.id;
      document.getElementById('agent-name').value = agent.name;
      document.getElementById('agent-description').value = agent.description;
      document.getElementById('agent-category').value = agent.category || '';
      document.getElementById('agent-bot-id').value = agent.bot_id;
      document.getElementById('agent-greeting').value = agent.greeting || '';

      // 选中对应的头像
      const avatarInput = document.querySelector(`#avatar-grid input[value="${agent.avatar}"]`);
      if (avatarInput) avatarInput.checked = true;

      openModal();
    }
    
    // 打开删除智能体确认模态框
    function openDeleteConfirmModal(agentId, agentName) {
      deleteAgentId = agentId;
      document.getElementById('delete-agent-name').textContent = agentName;
      document.getElementById('confirm-modal').classList.add('active');
    }

    // 关闭删除智能体确认模态框
    function closeConfirmModal() {
      deleteAgentId = null;
      document.getElementById('confirm-modal').classList.remove('active');
    }

    // 确认删除智能体
    function confirmDeleteAgent() {
      if (!deleteAgentId) return;

      const agents = getAgentsFromStorage();
      const updatedAgents = agents.filter(agent => agent.id !== deleteAgentId);

      saveAgentsToStorage(updatedAgents);
      loadAgents();

      showAlert('智能体已成功删除。', 'success');
      closeConfirmModal();
    }

    // 打开删除分类确认模态框
    function openDeleteCategoryConfirmModal(categoryId, categoryName) {
      deleteCategoryId = categoryId;
      document.getElementById('delete-category-name').textContent = categoryName;
      document.getElementById('confirm-category-modal').classList.add('active');
    }

    // 关闭删除分类确认模态框
    function closeConfirmCategoryModal() {
      deleteCategoryId = null;
      document.getElementById('confirm-category-modal').classList.remove('active');
    }

    // 确认删除分类
    function confirmDeleteCategory() {
      if (!deleteCategoryId) return;

      const categories = getCategoriesFromStorage();
      const agents = getAgentsFromStorage();

      // 找到"未分类"分类，如果不存在则创建
      let uncategorizedCategory = categories.find(c => c.name === '未分类');
      if (!uncategorizedCategory) {
        uncategorizedCategory = { id: generateId(), name: '未分类' };
        categories.push(uncategorizedCategory);
      }

      // 将被删除分类下的智能体移动到"未分类"
      const updatedAgents = agents.map(agent => {
        if (agent.category === deleteCategoryId) {
          return { ...agent, category: uncategorizedCategory.id };
        }
        return agent;
      });

      // 删除分类
      const updatedCategories = categories.filter(category => category.id !== deleteCategoryId);

      saveCategoriesStorage(updatedCategories);
      saveAgentsToStorage(updatedAgents);

      loadCategories();
      loadAgents();

      showAlert('分类已成功删除，相关智能体已移动到"未分类"。', 'success');
      closeConfirmCategoryModal();
    }
    
    // 保存分类
    function saveCategory() {
      const form = document.getElementById('category-form');

      // 表单验证
      if (!form.checkValidity()) {
        form.reportValidity();
        return;
      }

      const categoryId = document.getElementById('category-id-field').value || generateId();
      const name = document.getElementById('category-name').value;

      const categories = getCategoriesFromStorage();

      // 检查分类名称是否重复
      const existingCategory = categories.find(c => c.name === name && c.id !== currentEditingCategoryId);
      if (existingCategory) {
        showAlert('分类名称已存在，请使用其他名称。', 'warning');
        return;
      }

      if (currentEditingCategoryId) {
        // 编辑现有分类
        const index = categories.findIndex(category => category.id === currentEditingCategoryId);
        if (index !== -1) {
          categories[index] = {
            id: categoryId,
            name
          };
        }
      } else {
        // 添加新分类
        categories.push({
          id: categoryId,
          name
        });
      }

      saveCategoriesStorage(categories);
      loadCategories();

      showAlert(`分类 "${name}" 已成功${currentEditingCategoryId ? '更新' : '添加'}。`, 'success');
      closeCategoryModal();
    }

    // 保存智能体
    function saveAgent() {
      const form = document.getElementById('agent-form');

      // 表单验证
      if (!form.checkValidity()) {
        form.reportValidity();
        return;
      }

      const agentId = document.getElementById('agent-id-field').value || generateId();
      const name = document.getElementById('agent-name').value;
      const description = document.getElementById('agent-description').value;
      const category = document.getElementById('agent-category').value;
      const botId = document.getElementById('agent-bot-id').value;
      const greeting = document.getElementById('agent-greeting').value;
      const avatar = document.querySelector('#avatar-grid input[type="radio"]:checked').value;

      const agents = getAgentsFromStorage();

      if (currentEditingId) {
        // 编辑现有智能体
        const index = agents.findIndex(agent => agent.id === currentEditingId);
        if (index !== -1) {
          agents[index] = {
            id: agentId,
            name,
            description,
            category,
            bot_id: botId,
            greeting,
            avatar
          };
        }
      } else {
        // 添加新智能体
        agents.push({
          id: agentId,
          name,
          description,
          category,
          bot_id: botId,
          greeting,
          avatar
        });
      }

      saveAgentsToStorage(agents);
      loadAgents();

      showAlert(`智能体 "${name}" 已成功${currentEditingId ? '更新' : '添加'}。`, 'success');
      closeModal();
    }
    
    // 从本地存储获取智能体配置
    function getAgentsFromStorage() {
      const agentsJson = localStorage.getItem(STORAGE_KEY);
      return agentsJson ? JSON.parse(agentsJson) : [];
    }

    // 保存智能体配置到本地存储
    function saveAgentsToStorage(agents) {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(agents));
    }

    // 从本地存储获取分类配置
    function getCategoriesFromStorage() {
      const categoriesJson = localStorage.getItem(CATEGORIES_STORAGE_KEY);
      return categoriesJson ? JSON.parse(categoriesJson) : [];
    }

    // 保存分类配置到本地存储
    function saveCategoriesStorage(categories) {
      localStorage.setItem(CATEGORIES_STORAGE_KEY, JSON.stringify(categories));
    }
    
    // 生成唯一ID
    function generateId() {
      return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
    }
    
    // 打开智能体模态框
    function openModal() {
      document.getElementById('agent-modal').classList.add('active');
    }

    // 关闭智能体模态框
    function closeModal() {
      document.getElementById('agent-modal').classList.remove('active');
    }

    // 打开分类模态框
    function openCategoryModal() {
      document.getElementById('category-modal').classList.add('active');
    }

    // 关闭分类模态框
    function closeCategoryModal() {
      document.getElementById('category-modal').classList.remove('active');
    }
    
    // 显示提示信息
    function showAlert(message, type) {
      const alertsContainer = document.getElementById('alerts-container');
      
      const alert = document.createElement('div');
      alert.className = `alert alert-${type}`;
      alert.textContent = message;
      
      alertsContainer.appendChild(alert);
      
      // 5秒后自动移除提示
      setTimeout(() => {
        alert.remove();
      }, 5000);
    }
  </script>
</body>
</html>