<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <title>调试版本 - 微信式 Coze 聊天</title>
    <link rel="stylesheet" href="style.css" />
    <style>
        .debug-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            font-size: 12px;
            z-index: 9999;
            max-height: 400px;
            overflow-y: auto;
        }
        .debug-log {
            background: #f1f1f1;
            padding: 5px;
            margin: 5px 0;
            border-radius: 3px;
            font-family: monospace;
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <div class="debug-panel">
        <h4>🔧 调试面板</h4>
        <div id="debug-logs"></div>
        <button onclick="clearLogs()" style="margin-top: 10px; padding: 5px 10px; font-size: 11px;">清除日志</button>
    </div>

    <div class="chat-container">
        <div class="agent-sidebar">
            <div class="agent-item selected" onclick="selectAgent(this, '7523880023146168366')">
                <img src="touxiang/11.png" class="avatar" />
                <div class="agent-info">
                    <div class="name">Coze助手</div>
                    <div class="desc">智能AI助手，为您答疑解惑</div>
                </div>
            </div>
            <div class="agent-item" onclick="selectAgent(this, '7523880023146168366')">
                <img src="touxiang/12.png" class="avatar" />
                <div class="agent-info">
                    <div class="name">智能客服</div>
                    <div class="desc">专业的客服助手</div>
                </div>
            </div>
        </div>
        <div id="chat-box" class="chat-box"></div>
    </div>

    <script src="https://lf-cdn.coze.cn/obj/unpkg/flow-platform/chat-app-sdk/1.2.0-beta.10/libs/cn/index.js"></script>
    <script>
        let clientInstance = null;

        // 调试日志函数
        function debugLog(message, type = 'info') {
            const logsDiv = document.getElementById('debug-logs');
            const logDiv = document.createElement('div');
            logDiv.className = `debug-log ${type}`;
            logDiv.innerHTML = `${new Date().toLocaleTimeString()}: ${message}`;
            logsDiv.appendChild(logDiv);
            logsDiv.scrollTop = logsDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLogs() {
            document.getElementById('debug-logs').innerHTML = '';
        }

        // 检查DOM元素
        function checkDOMElements() {
            const chatBox = document.getElementById('chat-box');
            if (chatBox) {
                debugLog(`✅ chat-box 元素存在，尺寸: ${chatBox.offsetWidth}x${chatBox.offsetHeight}`, 'success');
            } else {
                debugLog('❌ chat-box 元素不存在', 'error');
            }
        }

        // 显示加载状态
        function showLoading() {
            const chatBox = document.getElementById("chat-box");
            chatBox.innerHTML = `
                <div style="display: flex; justify-content: center; align-items: center; height: 100%; color: #666; background: #f8f9fa;">
                    <div style="text-align: center;">
                        <div style="margin-bottom: 10px; font-size: 24px;">🤖</div>
                        <div>正在初始化AI助手...</div>
                    </div>
                </div>
            `;
            debugLog('显示加载状态', 'info');
        }

        function switchAgent(botId) {
            debugLog(`开始切换智能体: ${botId}`, 'info');
            
            // 检查DOM元素
            checkDOMElements();
            
            // 显示加载状态
            showLoading();

            // 销毁之前的实例
            if (clientInstance) {
                try {
                    clientInstance.destroy();
                    debugLog('销毁之前的客户端实例', 'success');
                } catch (error) {
                    debugLog(`销毁实例时出错: ${error.message}`, 'warning');
                }
            }

            try {
                const chatBox = document.getElementById("chat-box");
                debugLog(`准备初始化，容器元素: ${chatBox ? '存在' : '不存在'}`, 'info');
                
                // 清空容器
                chatBox.innerHTML = "";
                
                const config = {
                    config: {
                        bot_id: botId,
                    },
                    componentProps: {
                        title: 'Coze智能助手',
                    },
                    auth: {
                        type: "token",
                        token: "sat_sWfwyuKNho8ZHtkm2nmiXsXlFntAptsaa9XC6HpofAA3XLpTRoS5gGmkCJ2LpwaP",
                        onRefreshToken: function () {
                            debugLog('Token刷新请求', 'info');
                            return "sat_sWfwyuKNho8ZHtkm2nmiXsXlFntAptsaa9XC6HpofAA3XLpTRoS5gGmkCJ2LpwaP";
                        },
                    },
                    ui: {
                        asstBtn: {
                            isNeed: false, // 禁用悬浮球
                        },
                        chatBot: {
                            el: chatBox,
                            width: "100%",
                            height: "100%",
                        },
                    },
                };
                
                debugLog('配置对象创建完成', 'success');
                debugLog(`配置详情: bot_id=${botId}, el=${chatBox.id}`, 'info');
                
                clientInstance = new CozeWebSDK.WebChatClient(config);
                debugLog('✅ 聊天客户端初始化成功', 'success');

                // 由于禁用了悬浮球，需要手动显示聊天窗口
                setTimeout(() => {
                    if (clientInstance && clientInstance.showChatBot) {
                        clientInstance.showChatBot();
                        debugLog('手动显示聊天窗口', 'success');
                    } else {
                        debugLog('无法调用showChatBot方法', 'warning');
                    }
                }, 500);
                
            } catch (error) {
                debugLog(`❌ 初始化失败: ${error.message}`, 'error');
                debugLog(`错误堆栈: ${error.stack}`, 'error');
                
                // 显示错误信息
                const chatBox = document.getElementById("chat-box");
                chatBox.innerHTML = `
                    <div style="display: flex; justify-content: center; align-items: center; height: 100%; color: #ff6b6b; background: #fff5f5;">
                        <div style="text-align: center; padding: 20px;">
                            <div style="margin-bottom: 10px; font-size: 24px;">❌</div>
                            <div>初始化失败</div>
                            <div style="margin-top: 10px; font-size: 12px;">${error.message}</div>
                        </div>
                    </div>
                `;
            }
        }

        function selectAgent(element, botId) {
            debugLog(`选择智能体: ${botId}`, 'info');
            
            // 移除所有选中状态
            document.querySelectorAll('.agent-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 添加选中状态
            element.classList.add('selected');
            
            // 切换智能体
            switchAgent(botId);
        }

        // 页面加载完成后初始化
        window.onload = () => {
            debugLog('页面加载完成', 'success');
            
            // 检查SDK加载状态
            if (typeof CozeWebSDK === 'undefined') {
                debugLog('❌ Coze SDK 加载失败', 'error');
                return;
            }
            debugLog('✅ Coze SDK 加载成功', 'success');
            
            // 检查DOM元素
            checkDOMElements();
            
            // 默认加载第一个智能体
            switchAgent("7523880023146168366");
        };
    </script>
</body>
</html>
