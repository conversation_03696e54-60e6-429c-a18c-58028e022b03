<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <title>Coze API 测试工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .test-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #374151;
        }
        input, textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            font-family: inherit;
        }
        textarea {
            min-height: 100px;
            resize: vertical;
        }
        .btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #2563eb;
            transform: translateY(-2px);
        }
        .btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
        }
        .result {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { border-color: #10b981; background: #f0fdf4; }
        .error { border-color: #ef4444; background: #fef2f2; }
        .info { border-color: #3b82f6; background: #eff6ff; }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 10px;
        }
        .status.success { background: #10b981; color: white; }
        .status.error { background: #ef4444; color: white; }
        .status.loading { background: #f59e0b; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Coze API 测试工具</h1>
            <p>测试和调试 Coze API 调用</p>
        </div>
        
        <div class="content">
            <!-- API 配置 -->
            <div class="test-section">
                <h3>🔧 API 配置</h3>
                <div class="form-group">
                    <label>Bot ID:</label>
                    <input type="text" id="bot-id" value="7523880023146168366" />
                </div>
                <div class="form-group">
                    <label>SAT Token:</label>
                    <input type="password" id="token" value="sat_sWfwyuKNho8ZHtkm2nmiXsXlFntAptsaa9XC6HpofAA3XLpTRoS5gGmkCJ2LpwaP" />
                </div>
                <div class="form-group">
                    <label>API Base URL:</label>
                    <input type="text" id="api-base" value="https://api.coze.cn/open_api/v2" />
                </div>
            </div>
            
            <!-- 连接测试 -->
            <div class="test-section">
                <h3>🔗 连接测试 <span id="connection-status" class="status">未测试</span></h3>
                <button class="btn" onclick="testConnection()">测试连接</button>
                <div id="connection-result" class="result" style="display: none;"></div>
            </div>
            
            <!-- 消息测试 -->
            <div class="test-section">
                <h3>💬 消息测试</h3>
                <div class="form-group">
                    <label>测试消息:</label>
                    <textarea id="test-message" placeholder="输入要测试的消息...">你好，请介绍一下你自己</textarea>
                </div>
                <button class="btn" onclick="testMessage()" id="test-btn">发送测试消息</button>
                <div id="message-result" class="result" style="display: none;"></div>
            </div>
            
            <!-- 批量测试 -->
            <div class="test-section">
                <h3>🚀 批量测试</h3>
                <p>测试多个预设消息，验证 API 稳定性</p>
                <button class="btn" onclick="runBatchTest()">运行批量测试</button>
                <div id="batch-result" class="result" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        let conversationId = null;

        // 测试连接
        async function testConnection() {
            const statusEl = document.getElementById('connection-status');
            const resultEl = document.getElementById('connection-result');
            
            statusEl.textContent = '测试中...';
            statusEl.className = 'status loading';
            resultEl.style.display = 'block';
            resultEl.textContent = '正在测试连接...\n';
            
            try {
                const botId = document.getElementById('bot-id').value;
                const token = document.getElementById('token').value;
                const apiBase = document.getElementById('api-base').value;
                
                const response = await fetch(`${apiBase}/chat`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        bot_id: botId,
                        user: "test_user",
                        query: "连接测试",
                        stream: false
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    statusEl.textContent = '连接成功';
                    statusEl.className = 'status success';
                    resultEl.className = 'result success';
                    resultEl.textContent = `✅ 连接成功！\n\n响应数据:\n${JSON.stringify(data, null, 2)}`;
                } else {
                    throw new Error(`HTTP ${response.status}: ${data.msg || response.statusText}`);
                }
                
            } catch (error) {
                statusEl.textContent = '连接失败';
                statusEl.className = 'status error';
                resultEl.className = 'result error';
                resultEl.textContent = `❌ 连接失败:\n\n${error.message}\n\n请检查:\n1. Bot ID 是否正确\n2. Token 是否有效\n3. 网络连接是否正常`;
            }
        }

        // 测试消息
        async function testMessage() {
            const btn = document.getElementById('test-btn');
            const resultEl = document.getElementById('message-result');
            const message = document.getElementById('test-message').value;
            
            if (!message.trim()) {
                alert('请输入测试消息');
                return;
            }
            
            btn.disabled = true;
            btn.textContent = '发送中...';
            resultEl.style.display = 'block';
            resultEl.className = 'result info';
            resultEl.textContent = `发送消息: "${message}"\n等待响应...\n`;
            
            try {
                const botId = document.getElementById('bot-id').value;
                const token = document.getElementById('token').value;
                const apiBase = document.getElementById('api-base').value;
                
                const requestBody = {
                    bot_id: botId,
                    user: "test_user_" + Date.now(),
                    query: message,
                    stream: false
                };
                
                if (conversationId) {
                    requestBody.conversation_id = conversationId;
                }
                
                const startTime = Date.now();
                const response = await fetch(`${apiBase}/chat`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const endTime = Date.now();
                const data = await response.json();
                
                if (response.ok) {
                    // 保存对话ID
                    if (data.conversation_id) {
                        conversationId = data.conversation_id;
                    }
                    
                    // 提取回复内容
                    let replyContent = '未找到回复内容';
                    if (data.messages && data.messages.length > 0) {
                        const assistantMessage = data.messages
                            .filter(msg => msg.role === 'assistant' && msg.type === 'answer')
                            .pop();
                        if (assistantMessage && assistantMessage.content) {
                            replyContent = assistantMessage.content;
                        }
                    }
                    
                    resultEl.className = 'result success';
                    resultEl.textContent = `✅ 消息发送成功！\n\n响应时间: ${endTime - startTime}ms\n对话ID: ${conversationId || '无'}\n\n🤖 AI 回复:\n${replyContent}\n\n📊 完整响应数据:\n${JSON.stringify(data, null, 2)}`;
                } else {
                    throw new Error(`HTTP ${response.status}: ${data.msg || response.statusText}`);
                }
                
            } catch (error) {
                resultEl.className = 'result error';
                resultEl.textContent = `❌ 消息发送失败:\n\n${error.message}`;
            } finally {
                btn.disabled = false;
                btn.textContent = '发送测试消息';
            }
        }

        // 批量测试
        async function runBatchTest() {
            const resultEl = document.getElementById('batch-result');
            resultEl.style.display = 'block';
            resultEl.className = 'result info';
            resultEl.textContent = '开始批量测试...\n\n';
            
            const testMessages = [
                '你好',
                '请写一个JavaScript函数',
                '解释一下什么是人工智能',
                '帮我制定一个学习计划',
                '如何解决网络连接问题'
            ];
            
            let successCount = 0;
            let totalTime = 0;
            
            for (let i = 0; i < testMessages.length; i++) {
                const message = testMessages[i];
                resultEl.textContent += `测试 ${i + 1}/${testMessages.length}: "${message}"\n`;
                
                try {
                    const startTime = Date.now();
                    // 这里可以调用 testMessage 的逻辑，但简化处理
                    const response = await fetch(`${document.getElementById('api-base').value}/chat`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${document.getElementById('token').value}`,
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify({
                            bot_id: document.getElementById('bot-id').value,
                            user: "batch_test_" + Date.now(),
                            query: message,
                            stream: false
                        })
                    });
                    
                    const endTime = Date.now();
                    const responseTime = endTime - startTime;
                    totalTime += responseTime;
                    
                    if (response.ok) {
                        successCount++;
                        resultEl.textContent += `  ✅ 成功 (${responseTime}ms)\n`;
                    } else {
                        resultEl.textContent += `  ❌ 失败 (HTTP ${response.status})\n`;
                    }
                } catch (error) {
                    resultEl.textContent += `  ❌ 错误: ${error.message}\n`;
                }
                
                // 添加延迟避免请求过于频繁
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            const avgTime = totalTime / testMessages.length;
            resultEl.textContent += `\n📊 批量测试完成:\n`;
            resultEl.textContent += `成功: ${successCount}/${testMessages.length}\n`;
            resultEl.textContent += `平均响应时间: ${avgTime.toFixed(0)}ms\n`;
            resultEl.textContent += `总耗时: ${totalTime}ms\n`;
            
            if (successCount === testMessages.length) {
                resultEl.className = 'result success';
            } else if (successCount > 0) {
                resultEl.className = 'result info';
            } else {
                resultEl.className = 'result error';
            }
        }

        // 页面加载时自动测试连接
        window.onload = function() {
            console.log('API 测试工具加载完成');
        };
    </script>
</body>
</html>
