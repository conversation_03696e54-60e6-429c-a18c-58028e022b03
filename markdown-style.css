/* Markdown 美化样式 - 基于 style.txt 设计 */

:root {
  --primary-color: #3b82f6; /* 蓝色 */
  --bg-color: #ffffff;
  --text-color: #1f2937;
  --muted-color: #6b7280;
  --border-color: #e5e7eb;
  --code-bg: #f3f4f6;
  --shadow-light: 0 4px 12px rgba(0, 0, 0, 0.05);
  --shadow-medium: 0 8px 24px rgba(0, 0, 0, 0.08);
  --radius-small: 6px;
  --radius-medium: 12px;
  --radius-large: 16px;
}

/* 主容器样式 */
.markdown-body {
  max-width: 100%;
  margin: 0;
  padding: 24px;
  background: var(--bg-color);
  border-radius: var(--radius-large);
  color: var(--text-color);
  line-height: 1.75;
  font-size: 15px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
  transition: all 0.3s ease-in-out;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* 标题样式 */
.markdown-body h1, 
.markdown-body h2, 
.markdown-body h3, 
.markdown-body h4, 
.markdown-body h5, 
.markdown-body h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 8px;
}

.markdown-body h1 { 
  font-size: 2em; 
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
}
.markdown-body h2 { 
  font-size: 1.5em; 
  color: #374151;
}
.markdown-body h3 { 
  font-size: 1.25em; 
  color: #4b5563;
}
.markdown-body h4 { 
  font-size: 1.1em; 
  color: #6b7280;
}

/* 段落样式 */
.markdown-body p {
  margin-bottom: 16px;
  text-align: justify;
}

/* 列表样式 */
.markdown-body ul, .markdown-body ol {
  padding-left: 2em;
  margin-bottom: 16px;
}

.markdown-body li {
  margin-bottom: 8px;
  line-height: 1.6;
}

.markdown-body ul li {
  list-style-type: none;
  position: relative;
}

.markdown-body ul li::before {
  content: "•";
  color: var(--primary-color);
  font-weight: bold;
  position: absolute;
  left: -1.2em;
}

/* 链接样式 */
.markdown-body a {
  color: var(--primary-color);
  text-decoration: none;
  border-bottom: 1px dashed var(--primary-color);
  transition: all 0.3s ease;
  padding-bottom: 1px;
}

.markdown-body a:hover {
  color: #2563eb;
  border-bottom-style: solid;
  background-color: rgba(59, 130, 246, 0.1);
  padding: 2px 4px;
  border-radius: var(--radius-small);
}

/* 引用样式 */
.markdown-body blockquote {
  border-left: 4px solid var(--primary-color);
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
  padding: 16px 20px;
  margin: 20px 0;
  color: var(--muted-color);
  font-style: italic;
  border-radius: var(--radius-medium);
  box-shadow: var(--shadow-light);
  position: relative;
}

.markdown-body blockquote::before {
  content: """;
  font-size: 3em;
  color: var(--primary-color);
  position: absolute;
  top: -10px;
  left: 10px;
  opacity: 0.3;
}

/* 代码样式 */
.markdown-body code {
  background: var(--code-bg);
  color: #111827;
  padding: 0.2em 0.4em;
  border-radius: var(--radius-small);
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.9em;
  border: 1px solid #e1e5e9;
}

.markdown-body pre {
  background: var(--code-bg);
  padding: 20px;
  overflow-x: auto;
  border-radius: var(--radius-medium);
  margin: 20px 0;
  font-size: 0.9em;
  position: relative;
  transition: all 0.3s ease;
  border: 1px solid #e1e5e9;
  box-shadow: var(--shadow-light);
}

.markdown-body pre:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

.markdown-body pre code {
  background: none;
  padding: 0;
  border: none;
  color: #24292e;
}

/* 表格样式 */
.markdown-body table {
  border-collapse: collapse;
  width: 100%;
  margin: 20px 0;
  border-radius: var(--radius-medium);
  overflow: hidden;
  box-shadow: var(--shadow-light);
}

.markdown-body th {
  background: linear-gradient(135deg, var(--primary-color) 0%, #2563eb 100%);
  color: white;
  font-weight: 600;
  padding: 12px 16px;
  text-align: left;
  border: none;
}

.markdown-body td {
  border: 1px solid var(--border-color);
  padding: 12px 16px;
  text-align: left;
  transition: background-color 0.2s ease;
}

.markdown-body tr:nth-child(even) td {
  background-color: #f9fafb;
}

.markdown-body tr:hover td {
  background-color: #f0f8ff;
}

/* 分隔线样式 */
.markdown-body hr {
  border: none;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  margin: 32px 0;
  border-radius: 1px;
}

/* 图片样式 */
.markdown-body img {
  max-width: 100%;
  height: auto;
  border-radius: var(--radius-medium);
  box-shadow: var(--shadow-light);
  transition: transform 0.3s ease;
}

.markdown-body img:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-medium);
}

/* 强调文本样式 */
.markdown-body strong {
  color: var(--primary-color);
  font-weight: 600;
}

.markdown-body em {
  color: var(--muted-color);
  font-style: italic;
}

/* 动画效果 */
.markdown-body {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 打字机效果类 */
.typing-effect {
  overflow: hidden;
  border-right: 2px solid var(--primary-color);
  white-space: nowrap;
  animation: typing 2s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: var(--primary-color); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .markdown-body {
    padding: 16px;
    font-size: 14px;
  }
  
  .markdown-body h1 { font-size: 1.8em; }
  .markdown-body h2 { font-size: 1.4em; }
  .markdown-body h3 { font-size: 1.2em; }
  
  .markdown-body pre {
    padding: 12px;
    font-size: 0.8em;
  }
  
  .markdown-body table {
    font-size: 0.9em;
  }
  
  .markdown-body th, .markdown-body td {
    padding: 8px 12px;
  }
}

/* 滚动条美化 */
.markdown-body::-webkit-scrollbar {
  width: 8px;
}

.markdown-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.markdown-body::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4px;
}

.markdown-body::-webkit-scrollbar-thumb:hover {
  background: #2563eb;
}
