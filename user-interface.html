<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <title>智能体助手</title>
  <link rel="stylesheet" href="style.css" />
  <style>
    :root {
      --primary-color: #2563EB;
      --secondary-color: #60A5FA;
      --bg-color: #F8FAFC;
      --secondary-bg: #E2E8F0;
      --text-color: #1E293B;
      --text-secondary: #64748B;
      --success-color: #10B981;
      --danger-color: #EF4444;
      --warning-color: #F59E0B;
    }

    body {
      font-family: "Helvetica Neue", Arial, sans-serif;
      background-color: var(--bg-color);
      color: var(--text-color);
      margin: 0;
      padding: 0;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      padding-bottom: 15px;
      border-bottom: 1px solid var(--secondary-bg);
    }

    .header h1 {
      margin: 0;
      color: var(--primary-color);
    }

    .btn {
      padding: 10px 16px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .btn-primary {
      background-color: var(--primary-color);
      color: white;
    }

    .btn-primary:hover {
      background-color: #1D4ED8;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
    }

    /* 分类折叠样式 */
    .category-section {
      margin-bottom: 30px;
      background-color: white;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      overflow: hidden;
    }

    .category-header {
      background: linear-gradient(135deg, var(--primary-color) 0%, #1E40AF 100%);
      color: white;
      padding: 15px 20px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      transition: all 0.3s ease;
    }

    .category-header:hover {
      background: linear-gradient(135deg, #1D4ED8 0%, #1E3A8A 100%);
    }

    .category-title {
      font-size: 18px;
      font-weight: 600;
      margin: 0;
    }

    .category-toggle {
      font-size: 20px;
      transition: transform 0.3s ease;
    }

    .category-section.collapsed .category-toggle {
      transform: rotate(-90deg);
    }

    .category-content {
      padding: 20px;
      transition: all 0.3s ease;
      max-height: 1000px;
      overflow: hidden;
    }

    .category-section.collapsed .category-content {
      max-height: 0;
      padding: 0 20px;
    }

    .agent-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 20px;
    }

    .agent-card {
      background-color: var(--bg-color);
      border-radius: 8px;
      padding: 15px;
      border: 1px solid var(--secondary-bg);
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .agent-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      border-color: var(--primary-color);
    }

    .agent-info {
      display: flex;
      align-items: center;
      gap: 15px;
      margin-bottom: 10px;
    }

    .agent-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid var(--primary-color);
    }

    .agent-name {
      font-size: 16px;
      font-weight: 600;
      margin: 0;
      color: var(--text-color);
    }

    .agent-description {
      font-size: 14px;
      color: var(--text-secondary);
      margin: 10px 0;
      line-height: 1.4;
    }

    .agent-greeting {
      font-size: 13px;
      color: var(--text-secondary);
      font-style: italic;
      background-color: rgba(37, 99, 235, 0.05);
      padding: 8px;
      border-radius: 4px;
      border-left: 3px solid var(--primary-color);
    }

    .empty-category {
      text-align: center;
      color: var(--text-secondary);
      font-style: italic;
      padding: 40px 20px;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .agent-grid {
        grid-template-columns: 1fr;
      }
      
      .container {
        padding: 15px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>智能体助手</h1>
      <button id="config-btn" class="btn btn-primary">
        <span>⚙️ 配置管理</span>
      </button>
    </div>

    <div id="categories-container">
      <!-- 分类和智能体将在这里显示 -->
    </div>
  </div>

  <script>
    // 存储键名
    const STORAGE_KEY = 'coze_agents_config';
    const CATEGORIES_STORAGE_KEY = 'coze_categories_config';
    
    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
      loadUserInterface();
      
      // 添加配置按钮事件
      document.getElementById('config-btn').addEventListener('click', function() {
        window.open('agent-config.html', '_blank');
      });
    });
    
    // 加载用户界面
    function loadUserInterface() {
      const categories = getCategoriesFromStorage();
      const agents = getAgentsFromStorage();
      const container = document.getElementById('categories-container');
      
      container.innerHTML = '';
      
      if (categories.length === 0 || agents.length === 0) {
        container.innerHTML = `
          <div class="empty-category">
            <h3>暂无智能体</h3>
            <p>请点击右上角的"配置管理"按钮添加智能体</p>
          </div>
        `;
        return;
      }
      
      // 按分类组织智能体
      categories.forEach(category => {
        const categoryAgents = agents.filter(agent => agent.category === category.id);
        const categorySection = createCategorySection(category, categoryAgents);
        container.appendChild(categorySection);
      });
    }
    
    // 创建分类区域
    function createCategorySection(category, agents) {
      const section = document.createElement('div');
      section.className = 'category-section';
      section.innerHTML = `
        <div class="category-header" onclick="toggleCategory('${category.id}')">
          <h2 class="category-title">${category.name} (${agents.length})</h2>
          <span class="category-toggle">▼</span>
        </div>
        <div class="category-content" id="content-${category.id}">
          ${agents.length > 0 ? createAgentGrid(agents) : '<div class="empty-category">该分类下暂无智能体</div>'}
        </div>
      `;
      
      return section;
    }
    
    // 创建智能体网格
    function createAgentGrid(agents) {
      const grid = agents.map(agent => `
        <div class="agent-card" onclick="selectAgent('${agent.id}')">
          <div class="agent-info">
            <img src="${agent.avatar}" alt="${agent.name}" class="agent-avatar">
            <h3 class="agent-name">${agent.name}</h3>
          </div>
          <p class="agent-description">${agent.description}</p>
          ${agent.greeting ? `<div class="agent-greeting">"${agent.greeting}"</div>` : ''}
        </div>
      `).join('');
      
      return `<div class="agent-grid">${grid}</div>`;
    }
    
    // 切换分类折叠状态
    function toggleCategory(categoryId) {
      const section = document.querySelector(`#content-${categoryId}`).parentElement;
      section.classList.toggle('collapsed');
    }
    
    // 选择智能体
    function selectAgent(agentId) {
      const agents = getAgentsFromStorage();
      const agent = agents.find(a => a.id === agentId);
      
      if (agent) {
        alert(`您选择了智能体：${agent.name}\nBot ID: ${agent.bot_id}\n\n${agent.greeting || '暂无开场白'}`);
        // 这里可以添加实际的智能体交互逻辑
      }
    }
    
    // 从本地存储获取数据
    function getAgentsFromStorage() {
      const agentsJson = localStorage.getItem(STORAGE_KEY);
      return agentsJson ? JSON.parse(agentsJson) : [];
    }
    
    function getCategoriesFromStorage() {
      const categoriesJson = localStorage.getItem(CATEGORIES_STORAGE_KEY);
      return categoriesJson ? JSON.parse(categoriesJson) : [];
    }
  </script>
</body>
</html>
