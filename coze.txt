安装并使用 Chat SDK
本文介绍如何安装并使用扣子 Chat SDK，开发者可以参考本文档在自己开发的网站中快速添加一个 AI 应用，为网站集成智能对话服务。​
浏览器兼容性​
Chat  SDK 的运行环境要求如下表所示。​
​
准备工作​
获取一个访问密钥，用于 Chat SDK 的身份认证与鉴权。​
体验或调试场景：建议开发者生成一个短期的个人访问令牌（PAT），快速跑通 Chat SDK 的整体流程。​
在授权页面添加新令牌，并添加权限，使用 Chat SDK 所需的完整权限列表可参考下文中的权限要求。具体步骤请参见​添加个人访问令牌。​
​
​​

​
​
线上环境：线上环境应使用 OAuth 鉴权方案或 SAT 鉴权方案。​
​
使用 Chat SDK 所需的完整权限列表可参考下文中的权限要求。​
​
权限要求​
通过扣子访问令牌进行 OpenAPI 的鉴权，支持个人访问密钥（PAT）、服务访问令牌（SAT）和 OAuth 访问密钥。关于三种访问密钥的区别，可参考​鉴权方式概述。对于已入驻的第三方渠道，也可以使用渠道申请的 OAuth 访问密钥鉴权，申请方式可参考​渠道入驻概述。​
访问密钥必须被授予指定的权限，才能使用 Chat SDK 的各项能力。Chat SDK 所需的权限列表如下：​
​
​
配置流程​
步骤一：发布智能体或 AI 应用​
在智能体或 AI 应用的发布页面，选择 Chat SDK，并单击发布。发布的详细流程可参考：​
​发布应用为 Chat SDK​
​发布智能体到 Chat SDK​
步骤二：获取安装代码​
进入发布页面复制 SDK 安装代码。​
​
步骤三：安装 SDK​
你可以直接在页面中通过 script 标签的形式加载 Chat SDK 的 js 代码，将步骤二中复制好的安装代码粘贴到网页的 <body> 区域中即可。​
步骤二中复制好的安装代码示例如下：​
​
​
步骤四：配置聊天框​
安装 Chat SDK 后，您现在可以初始化客户端。在页面中通过调用 CozeWebSDK.WebChatClient 来生成聊天框，当前页面中聊天框包括 PC 和移动端两种布局样式。在 PC 端中，聊天框位于页面右下角，移动端聊天框会铺满全屏。​
智能体配置​
调用 CozeWebSDK.WebChatClient 时，你需要配置以下参数：​
config：必选参数，表示智能体的配置信息。​
智能体需要设置的参数如下： ​
​
auth：表示鉴权方式。当未配置此参数时表示不鉴权。为了账号安全，建议配置鉴权。​
​
示例如下：​
​
​
扣子应用配置​
调用 CozeWebSDK.WebChatClient 时，你需要配置以下参数：​
config：必选参数，表示应用的配置信息。​
​
auth：表示鉴权方式。当未配置此参数时表示不鉴权。为了账号安全，建议配置鉴权。​
​
示例如下：​
​
const cozeWebSDK = new CozeWebSDK.WebChatClient({​
  config: {​
    type: 'app',          // 应用类型​
    isIframe: false,      // 是否在iframe中运行​
    appInfo: {            // 应用配置信息​
      appId: '744189632066042****',​
      workflowId: '744229754050396****',​
      parameters: {       // 给自定义参数赋值并传给对话流​
        user_name: 'John'​
      }​
    }​
  },​
  auth:  {​
      // Authentication methods, the default type is 'unauth', which means no authentication is required; it is recommended to set it to 'token', indicating authentication through PAT (Personal Access Token) or OAuth​
    type: 'token',    ​
     // When the type is set to 'token', it is necessary to configure a PAT (Personal Access Token) or OAuth access token for authentication.​
    token: 'pat_zxzSAzxawer234zASNElEglZxcmWJ5ouCcq12gsAAsqJGALlq7hcOqMcPFV3wEVDiqjrg****',​
    // When the access token expires, use a new token and set it as needed.​
    onRefreshToken: () => 'pat_zxzSAzxawer234zASNElEglZxcmWJ5ouCcq12gsAAsqJGALlq7hcOqMcPFV3wEVDiqjrg****',​
  }​
});​
​
步骤五：配置属性​
扣子 Chat SDK 支持多种属性配置，开发者可以按需调整对话框的多种展示效果，例如展示的用户信息、对话框 UI 效果、悬浮球展示、底部文案等。​
你可以在 WebChatClient 方法中添加各种属性，实现对应的效果。目前支持的属性如下：​
​
步骤六：销毁客户端​
你可以添加一个 destroy 方法销毁客户端。 ​
​
属性配置​
用户信息​
userInfo 参数用于设置对话框中的显示用户信息，包括对话框中的用户头像和用户名。同时，此处指定的用户 ID 也会通过​发起对话 API 传递给扣子服务端。​
​
配置示例如下：​
​
UI 效果​
ui.base 参数用于添加聊天窗口的整体 UI 效果，包括应用图标、页面展示模式、语言属性等。​
​
示例代码如下：​
​
const cozeWebSDK = new CozeWebSDK.WebChatClient({​
  config: {​
    botId: '740849137970326****',​
    isIframe: false,​
  },​
  auth:  {​
    type: 'token',​
    token: 'pat_zxzSAzxawer234zASNElEglZxcmWJ5ouCcq12gsAAsqJGALlq7hcOqMcPFV3wEVDiqjrg****',​
    onRefreshToken: async () => 'pat_zxzSAzxawer234zASNElEglZxcmWJ5ouCcq12gsAAsqJGALlq7hcOqMcPFV3wEVDiqjrg****',​
  },​
  userInfo: {​
      id: '123',​
      url: 'https://lf-coze-web-cdn.coze.cn/obj/coze-web-cn/obric/coze/favicon.1970.png',​
      nickname: '3qweqweqwe',​
    },​
    ui: {​
      base: {​
          icon: 'https://lf-coze-web-cdn.coze.cn/obj/coze-web-cn/obric/coze/favicon.1970.png',​
          layout: 'pc',​
          zIndex: 1000,​
      }​
    },​
});​
​
悬浮球​
asstBtn 参数用于控制是否在页面右下角展示悬浮球。默认展示，用户点击悬浮球后将弹出聊天窗口。​
​
​​

​
​
若设置为不展示悬浮球，开发者需要通过以下方法控制聊天框的展示或隐藏效果。​
显示聊天框：cozeWebSDK.showChatBot()​
隐藏聊天框：cozeWebSDK.hideChatBot()​
​
以不展示悬浮球为例，示例代码如下：​
​
不展示悬浮球时，你可以通过以下方式显示聊天框或隐藏聊天框。​
​
底部文案​
聊天框底部会展示对话服务的提供方信息，默认为Powered by coze. AI-generated content for reference only.。开发者通过 footer 参数隐藏此文案或改为其他文案，支持在文案中设置超链接。​
底部文案默认展示效果如下：​
​
​​

​
​
footer 参数配置说明如下：​
​
配置示例如下：​
​
此配置的对应的展示效果如下：​
​
​​

​
​
顶部标题栏配置​
聊天框顶部默认展示智能体名称、icon、及关闭按钮。展示效果类似如下：​
​
​​

​
​
Chat SDK 1.1.0-beta.3 及以上版本支持该配置。​
​
您可以通过 header 参数配置是否展示顶部标题栏和关闭按钮，header 参数配置说明如下：​
​
配置示例如下：​
​
const cozeWebSDK = new CozeWebSDK.WebChatClient({​
  config: {​
    botId: '740849137970326****',​
    isIframe: false,​
  },​
  auth:  {​
    type: 'token',​
    token: 'pat_zxzSAzxawer234zASNElEglZxcmWJ5ouCcq12gsAAsqJGALlq7hcOqMcPFV3wEVDiqjrg****',​
    onRefreshToken: async () => 'pat_zxzSAzxawer234zASNElEglZxcmWJ5ouCcq12gsAAsqJGA*********',​
  },​
  userInfo: {​
      id: '123',​
      url: 'https://lf-coze-web-cdn.coze.cn/obj/coze-web-cn/obric/coze/favicon.1970.png',​
      nickname: '3qweqweqwe',​
    },​
    ui: {​
      base: {​
          icon: 'https://lf-coze-web-cdn.coze.cn/obj/coze-web-cn/obric/coze/favicon.1970.png',​
          layout: 'pc',​
          zIndex: 1000,​
      },​
      asstBtn: {​
          isNeed: true,​
      },​
      header: {​
          isShow: true,​
          isNeedClose: true,​
       },​
    },​
});​
​
聊天框​
chatBot 参数用于控制聊天框的 UI 和基础能力，包括标题、大小、位置等基本属性，还可以指定是否支持在聊天框中上传文件。此参数同时提供多个回调方法，用于同步聊天框显示、隐藏等事件通知。​
配置说明如下：​
​
相关回调：​
onHide：当聊天框隐藏的时候，会回调该方法。​
onShow: 当聊天框显示的时候，会回调该方法。​
onBeforeShow: 聊天框显示前调用，如果返回了 false，则不会显示。支持异步函数。​
onBeforeHide:  聊天框隐藏前调用，如果返回了 false，则不会隐藏。支持异步函数。​
在以下示例中，聊天框标题为 Kids' Playmate | Snowy，并开启上传文件功能。​
​
​​

​
​
对应的代码示例如下：​
​
通过 chatbot 的 el 参数设置组件的示例代码如下：​
​
消息评价​
你可以在 chatBot 参数中配置是否允许用户对智能体的回答进行评价（点赞 / 点踩），默认禁用评价功能。​
​
​​

​
​
​
示例代码如下：​
​
相关操作​
更新 SDK 版本​
扣子 Chat SDK 将持续更新迭代，支持丰富的对话能力和展示效果。你可以在 Chat SDK 的 script 标签中指定 Chat SDK 的最新版本号，体验和使用最新的 Chat SDK 对话效果。​
在以下代码中，将 {{version}} 部分替换为 Chat SDK 的最新版本号。你可以在​Chat SDK 发布历史中查看最新版本号。​
​
解绑 Chat SDK​
如果不再需要通过 Chat SDK 使用 AI 应用，可以在发布页面点击解绑按钮。一旦解绑，智能体或应用就无法通过集成的 Web 应用程序使用。 如果您想恢复 Web 应用程序的访问，需要再次将智能体或应用发布为 Chat SDK。 ​
​
示例​
以下是一段完整的 Chat SDK 调用智能体的代码示例。​
​
<!doctype html>​
<html lang="en">​
<head>​
    <style>​
        /* 自定义悬浮入口的位置 */​
        #position_demo {​
            position: absolute;​
            right: 10px;​
            bottom: 20px;​
        }​
    </style>​
</head>​
<body>​
<h1>Hello World</h1>​
<div id="position_demo"></div>​
    <script src="https://lf-cdn.coze.cn/obj/unpkg/flow-platform/chat-app-sdk/1.2.0-beta.8/libs/cn/index.js"></script>​
    <script>​
    const cozeWebSDK = new CozeWebSDK.WebChatClient({​
  config: {​
      // 智能体 ID​
    botId: '742477211246629****',​
  },​
  auth:  {​
      //鉴权方式，默认type 为unauth，表示不鉴权；建议设置为 token，表示通过PAT或OAuth鉴权​
    type: 'token',    ​
     //type 为 token 时，需要设置PAT或OAuth访问密钥​
    token: 'pat_82GrrdfNWPMnlcY58r98Rzqiev2s5NyrqCR8Ypbh5hOomzZN4ivb325PZAd****',​
    //访问密钥过期时，使用的新密钥，可以按需设置。​
    onRefreshToken: () => 'pat_82GrrdfNWPMnlcY58r98Rzqiev2s5NyrqCR8Ypbh5hOomzZN4ivb325PZAdZ****',​
  },ui:{​
      chatBot: {​
        title: "智能客服",​
        uploadable: true​
      }}​
});​
</script>​
</body>​
</html>​
​
相关文档​
如果需要将不同业务侧用户的会话互相隔离开来，每个用户只能看到自己和智能体的对话历史，请参见​如何实现会话隔离。