<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <title>Markdown 渲染测试</title>
    <link rel="stylesheet" href="style.css" />
    <link rel="stylesheet" href="markdown-style.css" />
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/lib/common.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/styles/github.min.css" />
    <script src="https://lf-cdn.coze.cn/obj/unpkg/flow-platform/chat-app-sdk/1.2.0-beta.10/libs/cn/index.js"></script>
    <style>
        .test-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            z-index: 9999;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .test-btn {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
        }
        .test-btn:hover {
            background: #2563eb;
        }
        .status {
            padding: 8px;
            border-radius: 4px;
            margin: 5px 0;
            font-size: 11px;
        }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="test-panel">
        <h4>🧪 Markdown 测试面板</h4>
        <div id="status-display" class="status warning">等待测试...</div>
        
        <button class="test-btn" onclick="testMarkdownDetection()">测试 Markdown 检测</button>
        <button class="test-btn" onclick="simulateAIResponse()">模拟 AI 回复</button>
        <button class="test-btn" onclick="injectTestMessage()">注入测试消息</button>
        <button class="test-btn" onclick="startObserverTest()">启动观察器测试</button>
        <button class="test-btn" onclick="clearChat()">清空聊天</button>
        
        <div style="margin-top: 10px; font-size: 10px; color: #666;">
            观察器状态: <span id="observer-status">未启动</span>
        </div>
    </div>

    <div class="chat-container">
        <div class="agent-sidebar">
            <div class="agent-item selected">
                <img src="touxiang/11.png" class="avatar" />
                <div class="agent-info">
                    <div class="name">Markdown 测试</div>
                    <div class="desc">测试 Markdown 渲染功能</div>
                </div>
            </div>
        </div>
        <div id="chat-box" class="chat-box"></div>
    </div>

    <script src="main.js"></script>
    <script>
        let testObserver = null;

        function updateStatus(message, type = 'warning') {
            const statusDiv = document.getElementById('status-display');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }

        function testMarkdownDetection() {
            const testTexts = [
                "# 这是标题\n\n这是普通文本",
                "```javascript\nconsole.log('hello');\n```",
                "**粗体文本** 和 *斜体文本*",
                "普通文本，没有 Markdown 语法"
            ];

            testTexts.forEach((text, index) => {
                const hasMarkdown = containsMarkdown(text);
                console.log(`测试 ${index + 1}: ${hasMarkdown ? '包含' : '不包含'} Markdown`);
            });

            updateStatus('Markdown 检测测试完成，查看控制台', 'success');
        }

        function simulateAIResponse() {
            const chatBox = document.getElementById('chat-box');
            const testMarkdown = `# 🤖 AI 助手回复

## 功能介绍

这是一个**测试回复**，包含多种 Markdown 元素：

### 代码示例

\`\`\`javascript
function greet(name) {
    console.log(\`Hello, \${name}!\`);
}
greet("World");
\`\`\`

### 列表示例

- ✅ 支持标题渲染
- ✅ 支持代码高亮
- ✅ 支持列表样式
- ✅ 支持表格显示

### 表格示例

| 功能 | 状态 | 说明 |
|------|------|------|
| 标题 | ✅ | 支持 H1-H6 |
| 代码 | ✅ | 语法高亮 |
| 表格 | ✅ | 美观样式 |

> 💡 **提示**: 这个回复应该被自动检测并渲染为美观的 Markdown 格式。

---

测试完成！`;

            // 创建一个模拟的消息元素
            const messageDiv = document.createElement('div');
            messageDiv.className = 'test-message';
            messageDiv.style.cssText = 'padding: 20px; margin: 10px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9;';
            messageDiv.textContent = testMarkdown;

            chatBox.appendChild(messageDiv);

            // 手动触发处理
            setTimeout(() => {
                processMessageElements(messageDiv);
                updateStatus('AI 回复模拟完成', 'success');
            }, 500);
        }

        function injectTestMessage() {
            const chatBox = document.getElementById('chat-box');
            
            // 创建一个包含 Markdown 的测试消息
            const testDiv = document.createElement('div');
            testDiv.innerHTML = `
                <div class="message-content">
                    <p># 测试标题</p>
                    <p>这是一个包含 **粗体** 和 *斜体* 的段落。</p>
                    <p>\`\`\`python
print("Hello, World!")
\`\`\`</p>
                </div>
            `;
            
            chatBox.appendChild(testDiv);
            updateStatus('测试消息已注入', 'success');
        }

        function startObserverTest() {
            const chatBox = document.getElementById('chat-box');
            
            if (testObserver) {
                testObserver.disconnect();
            }
            
            startMessageObserver(chatBox);
            document.getElementById('observer-status').textContent = '已启动';
            updateStatus('消息观察器已启动', 'success');
        }

        function clearChat() {
            const chatBox = document.getElementById('chat-box');
            chatBox.innerHTML = '';
            updateStatus('聊天内容已清空', 'success');
        }

        // 页面加载时自动启动观察器
        window.onload = function() {
            // 检查依赖
            if (typeof marked === 'undefined') {
                updateStatus('Marked.js 未加载', 'error');
                return;
            }
            
            if (typeof hljs === 'undefined') {
                updateStatus('Highlight.js 未加载', 'error');
                return;
            }
            
            updateStatus('依赖库加载完成', 'success');
            
            // 自动启动观察器测试
            setTimeout(() => {
                startObserverTest();
            }, 1000);
        };
    </script>
</body>
</html>
