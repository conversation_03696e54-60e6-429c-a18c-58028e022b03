<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <title>Markdown 美化效果演示</title>
    <link rel="stylesheet" href="markdown-style.css" />
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/lib/common.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/styles/github.min.css" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .demo-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .demo-header {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .demo-content {
            padding: 0;
        }
        .control-panel {
            background: #f8fafc;
            padding: 15px;
            border-bottom: 1px solid #e2e8f0;
            text-align: center;
        }
        .btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin: 0 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #2563eb;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🎨 Markdown 美化效果演示</h1>
            <p>展示 AI 回复的 Markdown 内容美化效果</p>
        </div>
        
        <div class="control-panel">
            <button class="btn" onclick="showDemo1()">📝 基础演示</button>
            <button class="btn" onclick="showDemo2()">💻 代码演示</button>
            <button class="btn" onclick="showDemo3()">📊 表格演示</button>
            <button class="btn" onclick="showDemo4()">🎯 综合演示</button>
            <button class="btn" onclick="clearDemo()">🗑️ 清空</button>
        </div>
        
        <div class="demo-content">
            <div id="markdown-output"></div>
        </div>
    </div>

    <script>
        // 配置 marked
        marked.setOptions({
            highlight: function(code, lang) {
                if (hljs.getLanguage(lang)) {
                    try {
                        return hljs.highlight(code, { language: lang }).value;
                    } catch (err) {}
                }
                return code;
            },
            breaks: true,
            gfm: true
        });

        function renderMarkdown(mdText) {
            const output = document.getElementById('markdown-output');
            const html = marked.parse(mdText);
            output.innerHTML = `<div class="markdown-body">${html}</div>`;
            
            // 高亮代码块
            output.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightElement(block);
            });
        }

        function showDemo1() {
            const markdown = `# 🤖 AI 助手回复示例

## 关于 Markdown 渲染

这是一个**美化的 Markdown 渲染**示例，展示了如何让 AI 的回复更加美观。

### 主要特性

- ✨ **美观的排版**：清晰的层级结构
- 🎨 **现代化设计**：蓝白配色方案
- 📱 **响应式布局**：适配各种屏幕尺寸
- 🔗 **交互效果**：悬停动画和过渡效果

> 💡 **提示**：这种渲染方式可以让用户更好地阅读和理解 AI 的回复内容。

---

希望这个演示对您有帮助！`;
            
            renderMarkdown(markdown);
        }

        function showDemo2() {
            const markdown = `# 💻 代码示例演示

## JavaScript 代码

以下是一个简单的 JavaScript 函数：

\`\`\`javascript
function greetUser(name) {
    const greeting = \`Hello, \${name}!\`;
    console.log(greeting);
    return greeting;
}

// 调用函数
greetUser("World");
\`\`\`

## Python 代码

这是一个 Python 类的示例：

\`\`\`python
class AIAssistant:
    def __init__(self, name):
        self.name = name
        self.responses = []
    
    def respond(self, message):
        response = f"AI {self.name}: {message}"
        self.responses.append(response)
        return response

# 创建实例
assistant = AIAssistant("Coze")
print(assistant.respond("Hello!"))
\`\`\`

## 内联代码

您可以使用 \`console.log()\` 来输出调试信息，或者使用 \`document.getElementById()\` 来获取 DOM 元素。`;
            
            renderMarkdown(markdown);
        }

        function showDemo3() {
            const markdown = `# 📊 表格演示

## 功能对比表

| 功能 | 原始显示 | 美化后 | 说明 |
|------|----------|--------|------|
| 标题 | 普通文本 | ✅ 蓝色强调 | 层级清晰 |
| 代码 | 无高亮 | ✅ 语法高亮 | 易于阅读 |
| 链接 | 默认样式 | ✅ 悬停效果 | 交互友好 |
| 表格 | 简单边框 | ✅ 现代样式 | 美观大方 |

## 浏览器兼容性

| 浏览器 | 版本要求 | 支持状态 |
|--------|----------|----------|
| Chrome | 87+ | ✅ 完全支持 |
| Firefox | 78+ | ✅ 完全支持 |
| Safari | 14+ | ✅ 完全支持 |
| Edge | 88+ | ✅ 完全支持 |`;
            
            renderMarkdown(markdown);
        }

        function showDemo4() {
            const markdown = `# 🎯 综合功能演示

## 1. 文本格式化

这里有**粗体文本**、*斜体文本*和~~删除线文本~~。

## 2. 列表展示

### 无序列表
- 🎨 美观的视觉设计
- 📱 响应式布局
- ⚡ 流畅的动画效果
- 🔧 易于维护的代码

### 有序列表
1. 首先加载必要的库文件
2. 然后配置 Markdown 渲染器
3. 接着应用自定义样式
4. 最后测试各种功能

## 3. 引用块

> 💭 **设计理念**
> 
> 我们的目标是创建一个既美观又实用的 Markdown 渲染系统，让 AI 的回复内容更加易读和专业。

## 4. 代码示例

\`\`\`css
.markdown-body {
    max-width: 100%;
    padding: 24px;
    background: var(--bg-color);
    border-radius: 16px;
    line-height: 1.75;
}
\`\`\`

## 5. 链接测试

访问 [GitHub](https://github.com) 查看更多开源项目。

---

🎉 **恭喜！** 您已经看完了所有的演示内容。`;
            
            renderMarkdown(markdown);
        }

        function clearDemo() {
            document.getElementById('markdown-output').innerHTML = '';
        }

        // 页面加载时显示默认内容
        window.onload = function() {
            showDemo1();
        };
    </script>
</body>
</html>
