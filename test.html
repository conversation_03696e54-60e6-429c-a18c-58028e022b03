<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <title>Coze SDK 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        #chat-container {
            height: 500px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Coze SDK 测试页面</h1>
        
        <div class="test-info">
            <h3>测试信息</h3>
            <p><strong>Bot ID:</strong> 7523880023146168366</p>
            <p><strong>SDK版本:</strong> 1.2.0-beta.10</p>
            <p><strong>认证方式:</strong> SAT Token</p>
        </div>

        <div id="status-container">
            <div id="sdk-status" class="status warning">正在检查SDK加载状态...</div>
            <div id="init-status" class="status warning">等待初始化...</div>
        </div>

        <button onclick="initializeChat()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
            初始化聊天
        </button>

        <div id="chat-container"></div>
    </div>

    <script src="https://lf-cdn.coze.cn/obj/unpkg/flow-platform/chat-app-sdk/1.2.0-beta.10/libs/cn/index.js"></script>
    <script>
        let chatClient = null;

        // 检查SDK加载状态
        window.onload = function() {
            const sdkStatus = document.getElementById('sdk-status');
            if (typeof CozeWebSDK !== 'undefined') {
                sdkStatus.className = 'status success';
                sdkStatus.textContent = '✅ Coze SDK 加载成功';
            } else {
                sdkStatus.className = 'status error';
                sdkStatus.textContent = '❌ Coze SDK 加载失败';
            }
        };

        function initializeChat() {
            const initStatus = document.getElementById('init-status');
            const chatContainer = document.getElementById('chat-container');
            
            try {
                initStatus.className = 'status warning';
                initStatus.textContent = '正在初始化聊天客户端...';

                chatClient = new CozeWebSDK.WebChatClient({
                    config: {
                        bot_id: '7523880023146168366',
                    },
                    componentProps: {
                        title: 'Coze测试助手',
                    },
                    auth: {
                        type: 'token',
                        token: 'sat_sWfwyuKNho8ZHtkm2nmiXsXlFntAptsaa9XC6HpofAA3XLpTRoS5gGmkCJ2LpwaP',
                        onRefreshToken: function () {
                            console.log('Token刷新请求');
                            return 'sat_sWfwyuKNho8ZHtkm2nmiXsXlFntAptsaa9XC6HpofAA3XLpTRoS5gGmkCJ2LpwaP';
                        }
                    }
                });

                initStatus.className = 'status success';
                initStatus.textContent = '✅ 聊天客户端初始化成功！您应该能看到聊天界面了。';
                
            } catch (error) {
                console.error('初始化失败:', error);
                initStatus.className = 'status error';
                initStatus.textContent = '❌ 初始化失败: ' + error.message;
            }
        }
    </script>
</body>
</html>
