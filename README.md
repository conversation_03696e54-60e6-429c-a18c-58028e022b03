# 微信式 Coze 聊天项目

这是一个基于 Coze Chat SDK 的微信风格聊天界面项目。

## 项目特点

- 🎨 微信风格的UI设计
- 🤖 支持多个AI智能体切换
- 📱 响应式布局，支持PC和移动端
- 🔒 完整的认证配置
- ⚡ 错误处理和加载状态
- 🌐 浏览器兼容性检查
- ✨ **美化的Markdown渲染**
- 🚫 **禁用流式输出**，整体文档一次性显示
- 🎯 **语法高亮**，代码块美观显示
- 💫 **动画效果**，提升用户体验
- 🎨 **纯自定义模式**，完美的Markdown渲染体验
- 🔗 **真实API对接**，使用 Coze 官方 API 进行对话

## 浏览器兼容性

- Chrome 87.0+
- Edge 88.0+
- Safari 14.0+
- Firefox 78.0+

## 项目结构

```
├── index.html          # 主页面
├── main.js            # 主要逻辑
├── style.css          # 基础样式文件
├── markdown-style.css  # Markdown美化样式
├── test.html          # 测试页面
├── debug.html         # 调试页面
├── markdown-demo.html  # Markdown演示页面
├── markdown-test.html  # Markdown功能测试页面
├── custom-chat.html   # 自定义聊天界面（完美支持Markdown）
├── api-test.html      # Coze API 测试工具
├── touxiang/          # 头像文件夹
│   ├── 11.png
│   ├── 12.png
│   ├── 13.png
│   └── ...
└── README.md          # 说明文档
```

## 智能体配置

- **Bot ID**: `7523880023146168366`
- **SDK版本**: `1.2.0-beta.10`
- **认证方式**: SAT Token

## 使用方法

1. 确保您有有效的 Coze SAT token
2. 在浏览器中打开 `index.html`
3. 点击左侧智能体列表切换不同的AI助手
4. 开始与AI对话

## 主要改进

### ✅ 已修复的问题

1. **认证配置** - 使用真实的SAT token替换占位符
2. **头像路径** - 修复头像文件路径，使用本地头像资源
3. **SDK配置** - 补充完整的配置参数
4. **错误处理** - 添加加载状态和错误处理机制
5. **SDK版本** - 更新为稳定版本
6. **浏览器兼容性** - 添加版本检查和提示

### 🎯 功能特性

- **智能体切换**: 支持多个AI助手，点击左侧列表即可切换
- **视觉反馈**: 选中的智能体会有高亮显示
- **加载状态**: 切换智能体时显示加载动画
- **错误处理**: 网络错误或初始化失败时显示友好提示
- **兼容性检查**: 自动检查浏览器版本并提示升级

## 技术栈

- Coze Chat SDK 1.2.0-beta.10
- 原生 HTML/CSS/JavaScript
- 响应式设计

## 测试方法

1. **🌟 主界面测试**: 打开 `index.html` 查看完美的自定义Markdown聊天界面
2. **功能测试**: 打开 `test.html` 进行SDK功能测试
3. **调试测试**: 打开 `debug.html` 查看详细调试信息
4. **Markdown演示**: 打开 `markdown-demo.html` 查看美化效果
5. **Markdown测试**: 打开 `markdown-test.html` 测试渲染功能
6. **🌟 自定义聊天**: 打开 `custom-chat.html` 体验纯自定义模式（完美Markdown渲染）
7. **🔧 API测试**: 打开 `api-test.html` 测试和调试 Coze API 连接
8. **浏览器控制台**: 查看详细的调试信息和错误日志

## ✨ 纯自定义模式功能

### 主界面 (`index.html`) 特性：

#### 🎨 **完美的自定义体验**
- 完全自定义的聊天界面设计
- 完美的 Markdown 渲染效果
- 非流式输出，整体内容一次性显示
- 最佳的视觉体验和用户交互
- 无需依赖 Coze SDK，纯前端实现

#### 🤖 **智能体系统**
- 4个专业智能体：Coze助手、智能客服、学习助手、编程助手
- 每个智能体有独特的回复风格和专业领域
- 个性化欢迎消息和专业建议
- 一键切换，无缝体验

#### 🎯 **核心优势**
- 🚀 **性能优异**: 无SDK依赖，加载速度快
- 🎨 **视觉完美**: 专为Markdown优化的界面设计
- 📱 **响应式**: 完美适配各种屏幕尺寸
- 🔧 **易维护**: 纯前端代码，结构清晰

## 🔗 真实 API 对接

### 主界面 (`index.html`) 现已对接真实 Coze API：

#### 🚀 **API 功能**
- **真实对话**: 直接调用 Coze API，获得真实的 AI 回复
- **对话上下文**: 支持多轮对话，维持上下文连续性
- **错误处理**: 完善的错误处理和用户友好的错误提示
- **连接检测**: 自动检测 API 连接状态

#### 🔧 **API 配置**
- **Bot ID**: `7523880023146168366`
- **SAT Token**: 使用您提供的真实 token
- **API 端点**: `https://api.coze.cn/open_api/v2/chat`
- **非流式**: 禁用流式输出，完整内容一次性显示

#### 🎯 **智能体角色**
- **Coze助手**: 通用AI助手
- **智能客服**: 带客服角色提示的专业服务
- **学习助手**: 带学习指导角色提示的教育助手
- **编程助手**: 带编程专家角色提示的技术助手

#### 🛠️ **API 测试工具**
打开 `api-test.html` 可以：
- 测试 API 连接状态
- 发送测试消息验证功能
- 运行批量测试检查稳定性
- 查看详细的 API 响应数据

## Markdown 美化功能

### ✨ 主要特性
- **禁用流式输出**: 整个回复内容一次性显示，避免逐字显示
- **语法高亮**: 代码块支持多种编程语言的语法高亮
- **美观排版**: 标题、列表、表格、引用等元素都有精美样式
- **响应式设计**: 在不同屏幕尺寸下都能保持良好显示效果
- **动画效果**: 内容加载时的淡入动画，提升用户体验

### 🎨 样式特点
- **蓝白配色**: 现代简洁的视觉风格
- **圆角设计**: 柔和的边框和阴影效果
- **悬停交互**: 链接、代码块等元素的悬停效果
- **字体优化**: 使用系统字体栈，确保最佳阅读体验

### 📚 支持的 Markdown 元素
- 标题 (H1-H6)
- 段落和换行
- **粗体** 和 *斜体* 文本
- 有序和无序列表
- 代码块和内联代码
- 表格
- 引用块
- 链接
- 分隔线
- 图片

## 注意事项

- 请确保网络连接正常，以便加载 Coze SDK
- 建议使用支持的浏览器版本以获得最佳体验
- SAT token 需要有相应的权限才能正常使用
