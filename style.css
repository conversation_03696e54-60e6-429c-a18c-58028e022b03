html, body {
  height: 100%;
  margin: 0;
  font-family: "Helvetica", "PingFang SC", sans-serif;
}

.chat-container {
  display: flex;
  height: 100vh;
  background-color: #f5f8ff;
}

.agent-sidebar {
  width: 260px;
  background: #ffffff;
  border-right: 1px solid #e0e0e0;
  overflow-y: auto;
}

.agent-item {
  display: flex;
  padding: 12px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
}

.agent-item:hover {
  background-color: #f0f8ff;
}

.agent-item.selected {
  background-color: #e6f3ff;
  border-left: 3px solid #007bff;
}

.agent-item.selected:hover {
  background-color: #e6f3ff;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.agent-info {
  margin-left: 12px;
  flex: 1;
}

.agent-info .name {
  font-weight: bold;
  font-size: 14px;
  color: #333;
}

.agent-info .desc {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.chat-box {
  flex: 1;
  position: relative;
  background: #ffffff;
  border-radius: 0 8px 8px 0;
  overflow: hidden;
}

/* 聊天框内的 Markdown 内容样式调整 */
.chat-box .markdown-body {
  margin: 0;
  padding: 20px;
  max-width: none;
  background: transparent;
  box-shadow: none;
  border-radius: 0;
}

/* 页面进场动画 */
body {
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}
